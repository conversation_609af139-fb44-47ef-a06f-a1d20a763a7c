name: accounting_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  sizer: ^3.0.5
  flutter_svg: ^2.0.17
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  # flutter_date_pickers: ^0.4.3
  intl: ^0.19.0
  pinput: ^5.0.1
  random_x: ^0.3.1
  emoji_picker_flutter: ^4.3.0
  camera: ^0.11.2
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/image/
    - assets/png/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: poppins_bold
      fonts:
        - asset: assets/font/Poppins-Bold.ttf
    - family: poppins_medium
      fonts:
        - asset: assets/font/Poppins-Medium.ttf
    - family: poppins_regular
      fonts:
        - asset: assets/font/Poppins-Regular.ttf
    - family: poppins_semibold
      fonts:
        - asset: assets/font/Poppins-SemiBold.ttf
    - family: gen_bold
      fonts:
        - asset: assets/font/GeneralSans-Bold.otf
    - family: gen_light
      fonts:
        - asset: assets/font/GeneralSans-Light.otf
    - family: gen_medium
      fonts:
        - asset: assets/font/GeneralSans-Medium.otf
    - family: gen_regular
      fonts:
        - asset: assets/font/GeneralSans-Regular.otf
    - family: gen_semibold
      fonts:
        - asset: assets/font/GeneralSans-Semibold.otf
    - family: pro_bold
      fonts:
        - asset: assets/font/Proxima Nova Bold.otf
    - family: pro_semibold
      fonts:
        - asset: assets/font/Proxima Nova Semibold.otf
    - family: pro_regular
      fonts:
        - asset: assets/font/ProximaNova-Regular.otf
    - family: cookie_regular
      fonts:
        - asset: assets/font/Cookie-Regular.ttf
    - family: inter_bold
      fonts:
        - asset: assets/font/Inter_18pt-Bold.ttf
    - family: inter_light
      fonts:
        - asset: assets/font/Inter_18pt-Light.ttf
    - family: inter_medium
      fonts:
        - asset: assets/font/Inter_18pt-Medium.ttf
    - family: inter_regular
      fonts:
        - asset: assets/font/Inter_18pt-Regular.ttf
    - family: inter_semibold
      fonts:
        - asset: assets/font/Inter_18pt-SemiBold.ttf
    - family: roboto_light
      fonts:
        - asset: assets/font/Roboto-Light.ttf
    - family: roboto_regular
      fonts:
        - asset: assets/font/Roboto-Regular.ttf
    - family: roboto_medium
      fonts:
        - asset: assets/font/Roboto-Medium.ttf
    - family: dm_bold
      fonts:
        - asset: assets/font/DMSans-Bold.ttf
    - family: dm_light
      fonts:
        - asset: assets/font/DMSans-Light.ttf
    - family: dm_medium
      fonts:
        - asset: assets/font/DMSans-Medium.ttf
    - family: dm_regular
      fonts:
        - asset: assets/font/DMSans-Regular.ttf
    - family: dm_semibold
      fonts:
        - asset: assets/font/DMSans-SemiBold.ttf
    - family: dm_thin
      fonts:
        - asset: assets/font/DMSans-Thin.ttf


  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
