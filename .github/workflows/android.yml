name: "Android Release"

on:
  push:
    branches:
      - main
      - master
      - develop
env:
  PROJECT: accounting_app

jobs:
  build:
    name: "Build & Release"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v2
        with:
          distribution: 'temurin'
          java-version: '17'
          cache: 'gradle'
      - uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
      - run: flutter pub get
      - run: flutter build apk --release --no-tree-shake-icons
      - name: Push to Releases
        uses: ncipollo/release-action@v1
        with:
          artifacts: "build/app/outputs/apk/release/*"
          tag: v0.0.${{ github.run_number }}
          token: ${{ secrets.TOKEN }}
      - name: Send FCM Message
        run: |
          FCM_URL="https://notify.mubashar.dev/send"
          FCM_PAYLOAD=$(cat <<EOF
          {
            "notification": {
              "title": "$PROJECT v0.0.${{ github.run_number }}",
              "body": "New version available"
            },
            "topic": "$PROJECT",
            "priority": "high",
            "data": {
              "repo": "$PROJECT"
            }
          }
          EOF
          )
          curl -X POST -H "Content-Type: application/json" -d "$FCM_PAYLOAD" $FCM_URL
