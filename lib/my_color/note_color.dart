import 'dart:ui';

/// Professional EU-compliant color palette following Material Design 3 guidelines
class NoteColors {
  // Primary Brand Colors - Professional Blue Palette
  static const Color primary = Color(0xFF1565C0); // Professional blue
  static const Color primaryVariant = Color(0xFF0D47A1); // Darker blue
  static const Color primaryLight = Color(0xFF42A5F5); // Light blue
  static const Color primaryContainer =
      Color(0xFFE3F2FD); // Very light blue container

  // Secondary Colors - Neutral Professional Palette
  static const Color secondary = Color(0xFF546E7A); // Professional gray-blue
  static const Color secondaryVariant = Color(0xFF37474F); // Darker gray-blue
  static const Color secondaryContainer =
      Color(0xFFECEFF1); // Light gray container

  // Surface Colors - Clean and Professional
  static const Color surface = Color(0xFFFAFAFA); // Off-white surface
  static const Color surfaceVariant = Color(0xFFF5F5F5); // Light gray surface
  static const Color background = Color(0xFFFFFFFF); // Pure white background
  static const Color backgroundSecondary =
      Color(0xFFF8F9FA); // Subtle gray background

  // Text Colors - High Contrast and Accessible
  static const Color onPrimary = Color(0xFFFFFFFF); // White text on primary
  static const Color onSecondary = Color(0xFFFFFFFF); // White text on secondary
  static const Color onSurface = Color(0xFF212121); // Dark text on surface
  static const Color onBackground =
      Color(0xFF212121); // Dark text on background

  // Text Hierarchy - Professional Typography Colors
  static const Color textPrimary =
      Color(0xFF212121); // Primary text - high emphasis
  static const Color textSecondary =
      Color(0xFF757575); // Secondary text - medium emphasis
  static const Color textTertiary =
      Color(0xFF9E9E9E); // Tertiary text - low emphasis
  static const Color textDisabled = Color(0xFFBDBDBD); // Disabled text

  // Semantic Colors - Status and Feedback
  static const Color success = Color(0xFF2E7D32); // Success green
  static const Color successLight =
      Color(0xFFE8F5E8); // Light success background
  static const Color warning = Color(0xFFED6C02); // Warning orange
  static const Color warningLight =
      Color(0xFFFFF3E0); // Light warning background
  static const Color error = Color(0xFFD32F2F); // Error red
  static const Color errorLight = Color(0xFFFFEBEE); // Light error background
  static const Color info = Color(0xFF0288D1); // Info blue
  static const Color infoLight = Color(0xFFE1F5FE); // Light info background

  // Border and Divider Colors
  static const Color border = Color(0xFFE0E0E0); // Standard border
  static const Color borderLight = Color(0xFFF0F0F0); // Light border
  static const Color divider = Color(0xFFE0E0E0); // Divider color

  // Interactive Colors - Buttons and Links
  static const Color interactive = Color(0xFF1976D2); // Interactive elements
  static const Color interactiveHover = Color(0xFF1565C0); // Hover state
  static const Color interactivePressed = Color(0xFF0D47A1); // Pressed state
  static const Color interactiveDisabled = Color(0xFFBDBDBD); // Disabled state

  // Shadow Colors
  static const Color shadow = Color(0x1A000000); // Standard shadow
  static const Color shadowLight = Color(0x0D000000); // Light shadow
  static const Color shadowDark = Color(0x33000000); // Dark shadow

  // ========== BACKWARD COMPATIBILITY ALIASES ==========
  // These maintain compatibility with existing code while transitioning to new system

  // Basic Colors
  static const Color white = background; // Pure white
  static const Color blue = primary; // Primary blue
  static const Color orange = warning; // Orange/warning color
  static const Color gryText = textSecondary; // Gray text
  static const Color blacked = textPrimary; // Black text
  static const Color darkblue = primaryVariant; // Dark blue
  static const Color green = success; // Success green
  static const Color blackbold = textPrimary; // Bold black text
  static const Color reds = error; // Error red
  static const Color yellow = Color(0xFFFFA726); // Yellow/amber
  static const Color bluePrimery = primary; // Primary blue (legacy spelling)

  // Legacy color definitions - keeping for backward compatibility
  static var f2f2 = Color(0xfff2f2f4);
  static var backgrond = Color(0x1a4a90e2);
  static var b3b3b3 = Color(0xFFB3B3B3);
  static var c6c6c6 = Color(0xffc6c6c6);
  static var f1f1 = Color(0xfff1f1f1);
  static var halfblue = Color(0xff4a90e2);
  static var hintColor = Color(0xff999999);
  static var d6d6d6 = Color(0xffd6d6d6);

  static var bluelight = Color(0xff2164f3);
  static var gry = Color(0xff8f8f8f);
  static var gryhigh = Color(0xff778598);

  static var starblue = Color(0xff167bd8);
  static var purple = Color(0xff917afd);
  static var deeppruple = Color(0xff03045e);
  static var blugry = Color(0xff808d9e);
  static var blulight = Color(0xff0077b6);
  static var greenlight = Color(0xff16d76f);

  static var halfblues = Color(0xff4a90e2);
  static var ff8f8f8 = Color(0xfff8f8fb);
  static var f8f8f8 = Color(0xff8f8f8f);
  static var textfieldborder = Color(0xffdedede);
  static var b333 = Color(0xff333333);
  static var pinput = Color(0xffebebeb);
  static var hihggry = Color(0xff374151);
  static var dc3 = Color(0xffd3cec4);
  static var textbron = Color(0xff625d52);
  static var bordr = Color(0xffffc4ae);
  static var backgrondcontnr = Color(0xffffece5);
  static var homebackgrund = Color(0xffffece5);
  static var bebe = Color(0xffbebebe);
  static var container = Color(0xfffba28a);
  static var gry83 = Color(0xff838383);
  static var d9d9 = Color(0xffd9d9d9);
  static var navigation = Color(0xffdad1f0);
  static var halfcolor = Color(0x99141b27);

  static var e5e5 = Color(0xffe5e7eb);
  static var f9fa = Color(0xfff9fafb);
  static var e6e6e6 = Color(0xffe6e6e6);
  static var lightpink = Color(0xffffece5);
  static var bobo = Color(0xffb0b0b0);
  static var redbach = Color(0xffef3c4e);
  static var time = Color(0xffaaa6b9);
  static var boldcount = Color(0xff524b6b);
  static var message = Color(0xff4b4657);
  static var red = Color(0xffb3261e);
  static var arrow = Color(0xff434343);
  static var cccc = Color(0xffcccccc);
  static var greenp = Color(0xff20c83c);
  static var grn = Color(0xff189e2f);
  static var bord = Color(0xffd3cec4);

  static var fieldborder = Color(0xff857f72);

  static var textclr = Color(0xff423d33);
  static var street = Color(0xff504a40);
  static var dede = Color(0x99dedbd3);
  static var dcd = Color(0xffd3cec4);
  static var fafa = Color(0xfffaf9f7);

  static var b4545 = Color(0xFF504A40);

  static var dedede = Color(0x4DEFEFEF);
  static var f7f7f7 = Color(0xFFF7F7F7);
  static var f1f1f1 = Color(0xFFF1F1F5);
  static var checkbox = Color(0xFF3DD598);
  static var b3b = Color(0xFF3B3B3B);
  static var d8 = Color(0x0d8d9baa);
  static var b888 = Color(0x1F888888);
  static var effa = Color(0xFFEFFAF3);
}
