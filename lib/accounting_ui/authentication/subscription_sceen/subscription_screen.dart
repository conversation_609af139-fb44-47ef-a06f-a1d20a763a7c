import 'package:accounting_app/accounting_ui/authentication/subscription_sceen/pay_now.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:accounting_app/my_widget/my_switch.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import '../../../item_list/item_lists.dart';
import '../../../my_widget/my_button.dart';
import '../../../my_widget/my_list_tile.dart';
class SubscriptionScreen extends StatelessWidget {
  const SubscriptionScreen({super.key});
  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    return Scaffold(
      backgroundColor: NoteColors.white,
      appBar: AppBar(
        backgroundColor: NoteColors.white,
        surfaceTintColor: NoteColors.white,
        automaticallyImplyLeading: false,
        actions: [
          Padding(
            padding:  EdgeInsets.only(right:  10.sp),
            child: IconButton(onPressed: (){
              context.back();
            }, icon: Icon(Icons.close,color: NoteColors.gry.withValues(alpha: .80),size: 22.sp,)),
          )
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: 10.sp,),
            Center(
              child: Text("Get Premium",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 22.sp,fontWeight: FontWeight.w400,
                  fontFamily: "gen_semibold",
                ),).marginSymmetric(horizontal: 20.sp),
            ),

            Center(
              child: Text(
                "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been....",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold.withValues(alpha: 0.50),
                  fontSize: 15.sp,fontWeight: FontWeight.w400,
                  fontFamily: "pro_regular",
                ),).marginSymmetric(horizontal: 20.sp),
            ),
            ListView.builder(
                scrollDirection: Axis.vertical,
                shrinkWrap: true,
                physics: BouncingScrollPhysics(),
                itemCount: subscription.length,
                itemBuilder: (context ,index) {
                  var item = subscription[index];
                  return GestureDetector(
                    onTap: (){
                      controller.current.value = index;
                    },
                    child: Padding(
                      padding:  EdgeInsets.only(left: 16.sp,right: 16.sp,top: 15.sp),
                      child:    Obx( () =>
                         Container(
                          height: 108,width: 340,
                        decoration: BoxDecoration(
                          color:controller.current.value == index ?  NoteColors.halfblue.withValues(alpha: .10) : NoteColors.white,
                          borderRadius: BorderRadius.circular(20.sp),
                          border: Border.all(  color:controller.current.value == index ?  NoteColors.halfblue: NoteColors.d6d6d6,)
                        ),
                          child: Column(
                            children: [
                              MyListTile(
                                images:  Icon( controller.current.value == index ? Icons.check_circle_rounded : Icons.radio_button_off_rounded, size: 20.sp,color:controller.current.value== index ?  NoteColors.bluePrimery : NoteColors.d9d9,),
                                title: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      item["title"]!,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: NoteColors.blackbold,
                                        fontSize: 18.sp,fontWeight: FontWeight.w600,
                                        fontFamily: "gen_semibold",
                                      ),),
                                  ],
                                ),
                                subtitle: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(item["sub"]!,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: NoteColors.blackbold.withValues(alpha: .50),
                                        fontSize: 15.sp,fontWeight: FontWeight.w400,
                                        fontFamily: "gen_regular",
                                      ),),
                                  ],
                                ),
                                trailing: Container(
                                  height: 26,width: 78,
                                  decoration: BoxDecoration(
                                    color:controller.current.value == index ?  NoteColors.bluePrimery : NoteColors.white,
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  child: Center(
                                      child: Text("50% OFF",style: TextStyle(color: NoteColors.white,fontFamily: "gen_semibold"),)),
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(item["price"]!,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: NoteColors.blackbold,
                                      fontSize: 15.sp,fontWeight: FontWeight.w400,
                                      fontFamily: "pro_regular",
                                    ),),
                                  if(controller.current.value == index)
                                  Text("  \$79.99/month",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: NoteColors.gry,
                                        fontSize: 15.sp,fontWeight: FontWeight.w400,
                                        fontFamily: "pro_regular",
                                      ),),
                                ],
                              ).marginOnly(left: 33.sp),
                            ],
                          ),
                                  ),
                      ),
                          ),
                  );

                }),

            Padding(
             padding:  EdgeInsets.only(left: 18.sp,right: 15.sp,top: 10.sp),
             child: Row(
               mainAxisAlignment: MainAxisAlignment.spaceBetween,
               children: [
                 Text("Auto recurring",
                   textAlign: TextAlign.center,
                   style: TextStyle(
                     color: NoteColors.blackbold,
                     fontSize: 16.sp,fontWeight: FontWeight.w400,
                     fontFamily: "pro_regular",
                   ),),
                 Obx(() =>
                    MySwitchButton(
                       value: controller.switched.value,
                   activeColor: NoteColors.bluePrimery,
                     onChange: (value) {
                         controller.toggleSwitch();
                     },
                   ),
                 )
               ],
             ),
           ),
            Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(   "\$79.99/month",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: NoteColors.gry,
                    fontSize: 15.sp,fontWeight: FontWeight.w400,
                    fontFamily: "pro_regular",
                  ),),
                  Text("  \$39.99/month (50% OFF)",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: NoteColors.blackbold,
                      fontSize: 16.sp,fontWeight: FontWeight.w500,
                      fontFamily: "pro_regular",
                    ),),
              ],
            ).marginSymmetric(horizontal: 30.sp),
            MyButton(
              onPressed:  (){
                context.openScreen(PayNow());
              },
              text: "Subscribe Now",fontFamily: "pro_bold",fontSize: 17.sp,
              paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
              textColor:  NoteColors.white,
              buttonColor:  NoteColors.bluePrimery,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.sp),
              ),
            ).marginSymmetric(horizontal: 20.sp,vertical: 10.sp),
            SizedBox(height:  15.sp,)
          ],
        ),
      ),
    );
  }
}
//
//Obx(()=>
//                         MyListTile(
//                           title: Text(item["name"] ?? "default name",style: TextStyle(fontFamily: "pro_regular",fontSize: 16.sp,fontWeight: FontWeight.w600,color: controller.current.value == index ? NoteColors.bluePrimery : NoteColors.blackbold.withValues(alpha: 0.80)),),
//                           trailing: controller.current.value == index ? Icon(Icons.check_circle_rounded ,color: NoteColors.bluePrimery,) : Icon(Icons.radio_button_off_outlined,color: NoteColors.white,),
//                         ),