import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:accounting_app/my_widget/my_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import '../../../my_controller/controller.dart';
import '../../main_screens/navigaion_screen.dart';
class PayNow extends StatelessWidget {
  const PayNow({super.key});
  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    return Scaffold(
      backgroundColor: NoteColors.white,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text("Pay Now",
          style: TextStyle(
          fontSize: 20.sp,
          fontFamily: "gen_semibold",
          fontWeight: FontWeight.w600,
        ),),
        centerTitle: true,
        backgroundColor: NoteColors.white,
        surfaceTintColor: NoteColors.white,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildTitle("Contact information's"),
              SizedBox(height: 10.sp,),
              Padding(
                padding:  EdgeInsets.only(left: 18.sp,right: 15.sp,),
                child: Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: NoteColors.gry.withValues(alpha: .50),
                        blurRadius: 5,spreadRadius: 0,offset: Offset(0, 2)
                      )
                    ]
                  ),
                  child: TextField(
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: NoteColors.white,
                      prefixIcon: Padding(
                        padding:  EdgeInsets.all(15.sp),
                        child: SvgPicture.asset("assets/image/Icons.svg",height: 20,width: 20,),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 15.sp),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14.sp),
                        borderSide: BorderSide(
                          color:  NoteColors.bluePrimery,
                        ),
                      ),
                      hintText: "<EMAIL>",
                      hintStyle: TextStyle(
                          color: NoteColors.gry,
                          fontFamily: "pro_medium",
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w400),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(15.sp),topRight: Radius.circular(15.sp)),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(14.sp),topRight: Radius.circular(14.sp)),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                    ),
          
                  ),
                ),
              ),
              Padding(
                padding:  EdgeInsets.only(left: 18.sp,right: 15.sp,),
                child: Container(
                  decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                            color: NoteColors.gry.withValues(alpha: .50),
                            blurRadius: 5,spreadRadius: 0,offset: Offset(0, 2)
                        )
                      ]
                  ),
                  child: TextField(
                    keyboardType: TextInputType.phone,
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: NoteColors.white,
                      contentPadding: EdgeInsets.symmetric(horizontal: 15.sp),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14.sp),
                        borderSide: BorderSide(
                          color:  NoteColors.bluePrimery,
                        ),
                      ),
                       prefixIcon: Padding(
                         padding:  EdgeInsets.all(15.sp),
                         child: SvgPicture.asset("assets/image/Flags.svg",height: 20,width: 20,),
                       ),
                      suffixIcon: Icon(Icons.error_outline_rounded,color: NoteColors.blackbold.withValues(alpha: .50),size: 20.sp,),
                      hintText: "(*************",
                      hintStyle: TextStyle(
                          color: NoteColors.gry,
                          fontFamily: "pro_medium",
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w400),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(15.sp),bottomRight: Radius.circular(15.sp)),
                        borderSide: BorderSide(
                          color:  NoteColors.gry.withValues(alpha: .20),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(14.sp),bottomRight: Radius.circular(14.sp)),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                    ),
          
                  ),
                ),
              ),
              SizedBox(height: 15.sp,),
              _buildTitle("Card information"),
              Padding(
                padding:  EdgeInsets.only(left: 18.sp,right: 15.sp,top: 12.sp),
                child: Container(
                  decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                            color: NoteColors.white.withValues(alpha: .50),
                            blurRadius: 0,spreadRadius: 0,offset: Offset(0, 0)
                        )
                      ]
                  ),
                  child: TextField(
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: NoteColors.white,
                     suffixIcon:     Row(
                       mainAxisAlignment: MainAxisAlignment.end,
                       children: [
                         Padding(
                           padding:  EdgeInsets.only(left: 8.sp,right: 8.sp),
                           child: SvgPicture.asset("assets/image/card1.svg",height: 18,width: 18,),
                         ),
                         Padding(
                           padding:  EdgeInsets.only(right: 8.sp),
                           child: SvgPicture.asset("assets/image/card2.svg",height: 18,width: 18,),
                         ),
                         Padding(
                           padding:  EdgeInsets.only(right: 8.sp),
                           child: SvgPicture.asset("assets/image/card3.svg",height: 18,width: 18,),
                         ),
                         Padding(
                           padding:  EdgeInsets.only(right: 8.sp),
                           child: SvgPicture.asset("assets/image/card4.svg",height: 18,width: 18,),
                         ),
                         SizedBox(width: 10.sp,)
                       ],
                     ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 15.sp),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14.sp),
                        borderSide: BorderSide(
                          color:  NoteColors.bluePrimery,
                        ),
                      ),
                      hintText: "1234 1234 1234 1234",
                      hintStyle: TextStyle(
                          color: NoteColors.gry,
                          fontFamily: "pro_medium",
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w400),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(15.sp),topRight: Radius.circular(15.sp)),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(14.sp),topRight: Radius.circular(14.sp)),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                    ),
          
                  ),
                ),
              ),
              Padding(
                padding:  EdgeInsets.only(left: 9.sp),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: 40,width: 163,
                      decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                                color: NoteColors.gry.withValues(alpha: .50),
                                blurRadius: 5,spreadRadius: 0,offset: Offset(0, 2)
                            )
                          ]
                      ),
                      child: TextField(
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                        LengthLimitingTextInputFormatter(5),
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: NoteColors.white,
          
                          contentPadding: EdgeInsets.symmetric(horizontal: 15.sp),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(14.sp),
                          ),
                          hintText: "MM/YY",
                          hintStyle: TextStyle(
                              color: NoteColors.gry,
                              fontFamily: "pro_regular",
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w400),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(15.sp),bottomRight: Radius.circular(0.sp)),
                            borderSide: BorderSide(
                              color:  NoteColors.gry.withValues(alpha: .20),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(14.sp),bottomRight: Radius.circular(0.sp)),
                            borderSide: BorderSide(
                              color:  NoteColors.d9d9,
                            ),
                          ),
                        ),
          
                      ),
                    ),
                    Container(
                      height: 40,width: 166,
                      decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                                color: NoteColors.gry.withValues(alpha: .50),
                                blurRadius: 5,spreadRadius: 0,offset: Offset(0, 2)
                            )
                          ]
                      ),
                      child: TextField(
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(3),
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: NoteColors.white,
                          contentPadding: EdgeInsets.symmetric(horizontal: 15.sp),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(14.sp),
                          ),
                          hintText: "CVC",
                          hintStyle: TextStyle(
                              color: NoteColors.gry,
                              fontFamily: "pro_regular",
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w400),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(0.sp),bottomRight: Radius.circular(15.sp)),
                            borderSide: BorderSide(
                              color:  NoteColors.gry.withValues(alpha: .20),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(0.sp),bottomRight: Radius.circular(14.sp)),
                            borderSide: BorderSide(
                              color:  NoteColors.d9d9,
                            ),
                          ),
                        ),
          
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.sp,),
              _buildTitle("Name on card"),
              Padding(
                padding:  EdgeInsets.only(left: 18.sp,right: 15.sp,top: 12.sp),
                child: Container(
                  decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                            color: NoteColors.gry.withValues(alpha: .50),
                            blurRadius: 5,spreadRadius: 0,offset: Offset(0, 2)
                        )
                      ]
                  ),
                  child: TextField(
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: NoteColors.white,
                      contentPadding: EdgeInsets.symmetric(horizontal: 15.sp),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14.sp),
                        borderSide: BorderSide(
                          color:  NoteColors.bluePrimery,
                        ),
                      ),
                      hintText: "Full name on card",
                      hintStyle: TextStyle(
                          color: NoteColors.gry,
                          fontFamily: "pro_medium",
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w400),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14.sp),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14.sp),
                        borderSide: BorderSide(
                          color:  NoteColors.d9d9,
                        ),
                      ),
                    ),
          
                  ),
                ),
              ),
              SizedBox(height: 10.sp,),
              _buildTitle("Country or region"),
          
              Obx(()=>
                 MyDropDown(
                   borderRadius: BorderRadius.only(topRight: Radius.circular(15.sp),topLeft: Radius.circular(15.sp),),
                  boxShadow: [
                    BoxShadow(
                      color: NoteColors.gry,
                      spreadRadius: 0,
                      blurRadius: 2,
                      offset: Offset(0, 1)
                    )
                  ],
                  selectedValue: controller.country.value,
                  items: controller.countryList,
                  onChanged: (String? value) {
                    if(value != null){
                      controller.selectCountry(value);
                    }
                  },),
              ),
              Container(
                height: 38,width: 325,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                      color: NoteColors.gry.withValues(alpha: .50),
                      blurRadius: 5,spreadRadius: 0,offset: Offset(0, 2)
                  )
                ],
                color: NoteColors.white,
                border: Border.all(
                    color: NoteColors.d9d9),
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(14.sp,),bottomRight: Radius.circular(14.sp)),
              ),child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding:  EdgeInsets.only(left: 12.sp),
                    child: Text("ZIP",style: TextStyle(color: NoteColors.gry,),),
                  ),
                ],
              ),),
              SizedBox(height: 20.sp,),
              MyButton(
                onPressed:  () {
                  context.openScreen(NavigaionScreen());
                },
                text: "Pay \$133.23",fontFamily: "pro_bold",fontSize: 17.sp,
                paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
                textColor:  NoteColors.white,
                buttonColor:  NoteColors.bluePrimery,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ).marginSymmetric(horizontal: 18.sp, vertical: 10.sp),
          
             SizedBox(height: 40.sp,),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Power by",
                    style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: NoteColors.gry.withValues(alpha: .50),
                        fontFamily: "gen_medium"),
                  ),
                  Text(
                    "  Stripe",
                    style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w900,
                        color: NoteColors.gryText,
                        fontFamily: "gen_semibold"),
                  ),
                ],
              ),
              SizedBox(height: 10.sp,),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [

                  Text(
                    "Terms",
                    style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w900,
                        color: NoteColors.gryText,
                        fontFamily: "gen_medium"),
                  ),
                  SizedBox(width: 15.sp,),
                  Text(
                    "Privacy",
                    style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w900,
                        color: NoteColors.gryText,
                        fontFamily: "gen_medium"),
                  ),
                ],
              ),
          
            ],
          ),
        ),
      ),
    );
  }
  Widget _buildTitle(String title){
    return Padding(
      padding: EdgeInsets.only(top: 10, left: 24, right: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: NoteColors.blackbold.withValues(alpha: .50),
              fontFamily: "sf_semibold",
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

//                             image:
//                                       AssetImage("assets/Images/bankcard2.png"),
//                                   height: 16,
//                                   width: 24,
//                                 ),
//                               ),
//                               Padding(
//                                 padding: EdgeInsets.all(3.0),
//                                 child: Image(
//                                   image:
//                                       AssetImage("assets/Images/bankcard3.png"),
//                                   height: 16,
//                                   width: 24,
//                                 ),
//                               ),
//                               Image(
//                                 image:
//                                     AssetImage("assets/Images/bankcard4.png"),
//                                 height: 16,
//                                 width: 24,
//                               ),
//                             ],
//                           ),
//                         ),
//                         focusedBorder: OutlineInputBorder(
//                             borderRadius: BorderRadius.circular(5),
//                             borderSide: BorderSide(
//                                 color: NoteColors.white.withOpacity(.8))),
//                         enabledBorder: OutlineInputBorder(
//                             borderSide: BorderSide(
//                                 color: NoteColors.white.withOpacity(.8)),
//                             borderRadius: BorderRadius.circular(
//                               5,
//                             ))),
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(top: 0, left: 20, right: 16),
//                 child: Row(
//                   children: [
//                     Container(
//                       height: 41,
//                       width: 160,
//                       decoration: BoxDecoration(
//                         color: NoteColors.f9f9,
//                         borderRadius: BorderRadius.only(
//                             bottomLeft: Radius.circular(6.25)),
//                         border: Border.all(color: NoteColors.grays),
//                       ),
//                       child: Center(
//                           child: Padding(
//                         padding: EdgeInsets.all(7.0),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           children: [
//                             Text(
//                               "MM/YY",
//                               style: TextStyle(
//                                   color: NoteColors.halfcolorf8f8,
//                                   fontFamily: "sf_regular",
//                                   fontWeight: FontWeight.w400,
//                                   fontSize: 10),
//                             ),
//                           ],
//                         ),
//                       )),
//                     ),
//                     Container(
//                       height: 41,
//                       width: 162,
//                       decoration: BoxDecoration(
//                         border: Border.all(color: NoteColors.grays),
//                         color: NoteColors.f9f9,
//                         borderRadius: BorderRadius.only(
//                             bottomRight: Radius.circular(6.25)),
//                       ),
//                       child: Center(
//                           child: Padding(
//                         padding: EdgeInsets.all(10.0),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text(
//                               "CVC",
//                               style: TextStyle(
//                                   color: NoteColors.halfcolorf8f8,
//                                   fontFamily: "sf_regular",
//                                   fontWeight: FontWeight.w400,
//                                   fontSize: 12),
//                             ),
//                             Image(
//                               image: AssetImage("assets/Images/Cards.png"),
//                               height: 16,
//                               width: 20,
//                             ),
//                           ],
//                         ),
//                       )),
//                     ),
//                   ],
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(top: 23, left: 24, right: 24),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   children: [
//                     Text(
//                       "Name on Card",
//                       style: TextStyle(
//                         fontSize: 14,
//                         fontFamily: "sf_medium",
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(
//                   top: 9,
//                   right: 16,
//                   left: 20,
//                 ),
//                 child: TextField(
//                   decoration: InputDecoration(
//                       filled: true,
//                       fillColor: NoteColors.f9f9,
//                       contentPadding:
//                           EdgeInsets.symmetric(horizontal: 15, vertical: 15),
//                       hintText: ('Full name on card'),
//                       hintStyle: TextStyle(
//                           fontFamily: "poppins_regular",
//                           fontSize: 12.5,
//                           fontWeight: FontWeight.w400,
//                           color: NoteColors.f8f8),
//                       focusedBorder: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(
//                             5,
//                           ),
//                           borderSide: BorderSide(
//                               color: NoteColors.grays.withOpacity(.8))),
//                       enabledBorder: OutlineInputBorder(
//                           borderSide: BorderSide(
//                               color: NoteColors.grays.withOpacity(.8)),
//                           borderRadius: BorderRadius.circular(
//                             5,
//                           ))),
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(top: 23, left: 24, right: 24),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   children: [
//                     Text(
//                       "Country or region",
//                       style: TextStyle(
//                         fontSize: 14,
//                         fontFamily: "sf_medium",
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(top: 10),
//                 child: Container(
//                   height: 45,
//                   width: 327,
//                   padding: EdgeInsets.symmetric(horizontal: 16),
//                   decoration: BoxDecoration(
//                     color: NoteColors.f9f9,
//                     border: Border.all(color: NoteColors.grays),
//                     borderRadius: BorderRadius.only(
//                         topLeft: Radius.circular(6.25),
//                         topRight: Radius.circular(6.25)),
//                   ),
//                   child: DropdownButtonHideUnderline(
//                     child: Obx(
//                       () => DropdownButton<String>(
//                         icon: Icon(
//                           Icons.keyboard_arrow_down_outlined,
//                           color: NoteColors.halfcolorf8f8,
//                         ),
//                         dropdownColor: NoteColors.scaffoldcolr,
//                         borderRadius: BorderRadius.circular(12),
//                         elevation: 0,
//                         hint: Text("Select country"),
//                         value: controller.selectedcountry.value,
//                         onChanged: (String? newValue) {
//                           controller.updatecontry(newValue!);
//                         },
//                         items: controller.contries.map((String value) {
//                           return DropdownMenuItem<String>(
//                             value: value,
//                             child: Text(
//                               value,
//                               style: TextStyle(
//                                   color: NoteColors.halfcolorf8f8,
//                                   fontWeight: FontWeight.w300,
//                                   fontFamily: "sf_regular",
//                                   fontSize: 12.5),
//                             ),
//                           );
//                         }).toList(),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//               Container(
//                 height: 45,
//                 width: 327,
//                 decoration: BoxDecoration(
//                   color: NoteColors.f9f9,
//                   borderRadius: BorderRadius.only(
//                       bottomLeft: Radius.circular(6.25),
//                       bottomRight: Radius.circular(6.25)),
//                 ),
//                 child: Padding(
//                   padding: EdgeInsets.all(10.0),
//                   child: Text(
//                     "ZIP",
//                     style: TextStyle(
//                         color: NoteColors.halfcolorf8f8,
//                         fontSize: 10,
//                         fontWeight: FontWeight.w400),
//                   ),
//                 ),
//               ),
//               Center(
//                 child: Padding(
//                   padding:
//                       EdgeInsets.only(top: 48, left: 14, right: 14, bottom: 10),
//                   child: Material(
//                     shadowColor: NoteColors.dpgradient,
//                     elevation: 1,
//                     borderRadius: BorderRadius.circular(30),
//                     child: Ink(
//                       decoration: BoxDecoration(
//                         gradient: LinearGradient(
//                           colors: [NoteColors.pgradient, NoteColors.dpgradient],
//                           begin: Alignment.topCenter,
//                           end: Alignment.bottomCenter,
//                         ),
//                         borderRadius: BorderRadius.circular(30),
//                       ),
//                       child: InkWell(
//                         onTap: () {
//                           Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                               builder: (context) => Ownercontect(),
//                             ),
//                           );
//                         },
//                         borderRadius: BorderRadius.circular(30),
//                         child: Container(
//                           height: 50,
//                           width: 434,
//                           alignment: Alignment.center,
//                           child: Text(
//                             "Pay now",
//                             textAlign: TextAlign.center,
//                             style: TextStyle(
//                               fontFamily: "sf_medium",
//                               color: Colors.white,
//                               fontSize: 16,
//                               fontWeight: FontWeight.w500,
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(top: 20, bottom: 15),
//                 child: InkWell(
//                   onTap: () {
//                     Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                             builder: (context) => Ratingscreen()));
//                   },
//                   child: Column(
//                     children: [
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Text(
//                             "Power by",
//                             style: TextStyle(
//                                 fontSize: 12,
//                                 fontWeight: FontWeight.w500,
//                                 color: NoteColors.halfcolorf8f8,
//                                 fontFamily: "sf_regular"),
//                           ),
//                           Text(
//                             "  Stripe",
//                             style: TextStyle(
//                                 fontSize: 12,
//                                 fontWeight: FontWeight.w900,
//                                 color: NoteColors.halfcolorf8f8,
//                                 fontFamily: "sf_bold"),
//                           ),
//                         ],
//                       ),
//                       SizedBox(
//                         height: 10,
//                       ),
//                       Text(
//                         " Terms     policies",
//                         style: TextStyle(
//                             fontSize: 12,
//                             fontWeight: FontWeight.w500,
//                             color: NoteColors.halfcolorf8f8,
//                             fontFamily: "sf_medium"),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
//
// class payController extends GetxController {
//   List<String> contries = ["USA", "Canada", "UNA", "PAK", "IND"].obs;
//   var selectedcountry = ("USA").obs;
//   void updatecontry(String newvalue) {
//     selectedcountry.value = newvalue;
//   }
// }