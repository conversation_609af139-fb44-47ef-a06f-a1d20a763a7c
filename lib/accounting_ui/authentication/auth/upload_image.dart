import 'dart:io';
import 'dart:math';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:accounting_app/my_widget/my_image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import 'home_land.dart';
class UploadImage extends StatelessWidget {
  const UploadImage({super.key});
  @override
  Widget build(BuildContext context) {
    final ImagePickerController imageController = Get.put(ImagePickerController());
    String image = "https://loremflickr.com/200/200?random=${Random().nextInt(1000)}";
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: NoteColors.white,
        surfaceTintColor: NoteColors.white,
        title: Stack(
          children: [
            Container(
              height: 8,width: 180,
              decoration: BoxDecoration(
                color: NoteColors.backgrond.withValues(alpha: 0.10),
                borderRadius: BorderRadius.circular(10.sp),
              ),
            ),
            Container(
              height: 8,width: 24,
              decoration: BoxDecoration(
                color: NoteColors.bluePrimery,
                borderRadius: BorderRadius.circular(10.sp),
              ),
            ),
          ],
        )
      ),
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: Column(
          children: [
            Center(
              child: Text("Upload your photo",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 19.sp,fontWeight: FontWeight.w600,
                  fontFamily: "poppins_regular",
                ),).marginSymmetric(horizontal: 20.sp),
            ),
            SizedBox(height: 14.sp,),
            Center(
              child: Text("We'd love to see you. Upload a photo for your dating journey.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 16.sp,fontWeight: FontWeight.w400,
                  fontFamily: "poppins_regular",
                ),).marginSymmetric(horizontal: 26.sp),
            ),
            SizedBox(height: 30.sp,),
            Obx(()=>
               Stack(
                children: [
                Container(
                height: 216,
                width: 206,
                decoration: BoxDecoration(
                  color: NoteColors.blue,
                  image: DecorationImage(
                    image: imageController.imagePath.value.isNotEmpty
                        ? FileImage(File(imageController.imagePath.value))
                        : CachedNetworkImageProvider(image) as ImageProvider,
                    fit: BoxFit.cover,
                  ),
                  shape: BoxShape.circle,
                ),
              ),
                Positioned(
                  left: 39.sp,top: 39.sp,
                  child: GestureDetector(
                        onTap : () async {
                          await  imageController.getImage();
                        },
                        child: SvgPicture.asset("assets/image/Vector (2).svg",height: 30,width: 30,)),
                ),
                ],
              ),
            ),
           SizedBox(
             height: 38.sp,),
           Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                      SvgPicture.asset("assets/image/face.svg",height: 17,width: 17,),
                      SizedBox(width: 34.sp,),
                      SvgPicture.asset("assets/image/suggestion.svg",height: 16,width: 25,),
                      SizedBox(width: 35.sp,),
                      SvgPicture.asset("assets/image/group.svg",height: 12,width: 19,),
              ],
            ),
            SizedBox(height: 10.sp,),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("Clear face",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: NoteColors.blackbold,
                    fontSize: 13.sp,fontWeight: FontWeight.w600,
                    fontFamily: "poppins_regular",
                  ),),
                SizedBox(width: 23.sp,),
                Text("No sunglasses",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: NoteColors.blackbold,
                    fontSize: 13.sp,fontWeight: FontWeight.w600,
                    fontFamily: "poppins_regular",
                  ),),
                SizedBox(width: 23.sp,),
                Text("No group",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: NoteColors.blackbold,
                    fontSize: 13.sp,fontWeight: FontWeight.w600,
                    fontFamily: "poppins_regular",
                  ),),
              ],
            ),
            Spacer(),
            MyButton(
              onPressed:  (){
               context.openScreen(HomeLand());
              },
              text: "Continue",fontFamily: "pro_bold",fontSize: 17.sp,
              paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
              textColor:  NoteColors.white,
              buttonColor:  NoteColors.bluePrimery,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.sp),
              ),
            ).marginSymmetric(horizontal: 20.sp,vertical: 20.sp),
          ],
        ),
      ),
    );
  }
}
