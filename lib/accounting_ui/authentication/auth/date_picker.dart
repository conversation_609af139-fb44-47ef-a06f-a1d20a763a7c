import 'package:accounting_app/accounting_ui/authentication/subscription_sceen/subscription_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
class DatePicker extends StatelessWidget {
  const DatePicker({super.key});
  @override
  Widget build(BuildContext context) {
    final DatePickerController controller = Get.put(DatePickerController());
    return Scaffold(
      backgroundColor: NoteColors.white,
      appBar: AppBar(
          centerTitle: true,
          backgroundColor: NoteColors.white,
          surfaceTintColor: NoteColors.white,
          title: Stack(
            children: [
              Container(
                height: 8, width: 180,
                decoration: BoxDecoration(
                  color: NoteColors.backgrond.withValues(alpha: 0.10),
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
              Container(
                height: 8, width: 178,
                decoration: BoxDecoration(
                  color: NoteColors.bluePrimery,
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
            ],
          )
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: 20.sp,),
            Center(
              child: Text("Provide your birth?",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 20.sp, fontWeight: FontWeight.w700,
                  fontFamily: "pro_medium",
                ),).marginSymmetric(horizontal: 20.sp),
            ),
            SizedBox(height: 5.sp,),
            Center(
              child: Text("Choose date & year",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 15.sp, fontWeight: FontWeight.w400,
                  fontFamily: "poppins_regular",
                ),).marginSymmetric(horizontal: 26.sp),
            ),

            Padding(
              padding: EdgeInsets.only(left: 18.sp, right: 20.sp, top: 25.sp),
              child: Obx(() {
                return TextField(
                  controller: controller.dateController.value,
                  onTap: () {
                    controller.selectDate(context);
                  },
                  readOnly: true,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: 15.sp, vertical: 10),
                    hintText: "${controller.selectedDate.value
                        .day}, ${controller.selectedDate.value
                        .month} ${controller.selectedDate.value.year}",
                    hintStyle: TextStyle(
                        color: NoteColors.blackbold,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w400,
                        fontFamily: "poppins_medium"
                    ),
                    suffixIcon: Padding(
                      padding: EdgeInsets.all(5),
                      child: SvgPicture.asset(
                          "assets/image/container.svg", height: 15, width: 15),
                    ),
                  ),
                );
              }),
            ),
            Spacer(),
            MyButton(
              onPressed: () {
                context.openScreen(SubscriptionScreen());
              },
              text: "Continue",
              fontFamily: "pro_bold",
              fontSize: 17.sp,
              paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
              textColor: NoteColors.white,
              buttonColor: NoteColors.bluePrimery,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.sp),
              ),
            ).marginSymmetric(horizontal: 20.sp, vertical: 20.sp)
          ],
        ),
      ),
    );
  }
}


///
//   Future<void> selectDate(BuildContext context) async {
//       DateTime? picked =  await showDatePicker(
//           context: context,
//           initialDate: selectedDate,
//           firstDate: DateTime(2000),
//           lastDate: DateTime(3000),
//           builder: (BuildContext context, Widget? child){
//             return Theme(data: ThemeData.light().copyWith(
//               colorScheme: ColorScheme.light(
//                 primary: Colors.black,
//                 onSurface: NoteColors.blackbold,
//               ),
//               textButtonTheme:  TextButtonThemeData(
//                   style: TextButton.styleFrom(
//                     backgroundColor: NoteColors.white,
//                     surfaceTintColor: NoteColors.blackbold,
//                   )
//               ),
//             ), child: child!);
//           }
//       );
//       if(picked != null) {
//         setState(() {
//           selectedDate = picked;
//           dateController.text = DateFormat('EEE, d MMM yyyy').format(selectedDate);
//         });
//       }
//     }