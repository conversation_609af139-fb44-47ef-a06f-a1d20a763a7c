import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import '../../../item_list/item_lists.dart';
import 'date_picker.dart';
class GenderScreen extends StatelessWidget {
  const GenderScreen({super.key});
  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    return Scaffold(
      backgroundColor: NoteColors.white,
      appBar: AppBar(
          centerTitle: true,
          backgroundColor: NoteColors.white,
          surfaceTintColor: NoteColors.white,
          title: Stack(
            children: [
              Container(
                height: 8,width: 180,
                decoration: BoxDecoration(
                  color: NoteColors.backgrond.withValues(alpha: 0.10),
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
              Container(
                height: 8,width: 120,
                decoration: BoxDecoration(
                  color: NoteColors.bluePrimery,
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
            ],
          )
      ),
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: 20.sp,),
            Center(
              child: Text(
                "What’s Your Gender?",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 20.sp,fontWeight: FontWeight.w700,
                  fontFamily: "pro_medium",
                ),).marginSymmetric(horizontal: 20.sp),
            ),
            SizedBox(height: 12.sp,),
            Center(
              child: Text("Tell us about your gender",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 15.sp,fontWeight: FontWeight.w400,
                  fontFamily: "poppins_regular",
                ),).marginSymmetric(horizontal: 26.sp),
            ),
            ListView.builder(
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  physics: BouncingScrollPhysics(),
                  itemCount: gender.length,
                  itemBuilder: (context ,index) {
                    var item = gender[index];
                    return Padding(
                      padding:  EdgeInsets.only(left: 16.sp,right: 16.sp),
                      child:
                          GestureDetector(
                            onTap: (){
                              controller.current.value = index;
                            },
                            child: Obx(()=>
                               Padding(
                                 padding:  EdgeInsets.only(top: 20.sp),
                                 child: Container(
                                  height: 150,
                                  width: 150,
                                  decoration: BoxDecoration(
                                    color:  controller.current.value == index ? NoteColors.bluePrimery : NoteColors.d9d9,
                                    shape: BoxShape.circle,
                                  ),
                                  child:    Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      item["image"] != null && item["image"]!.isNotEmpty
                                          ? SvgPicture.asset(
                                        item["image"]!,
                                        height: 50,
                                        width: 50,
                                        color: controller.current.value == index ?  NoteColors.white : NoteColors.blackbold,
                                      )
                                      : SvgPicture.asset(
                                        "assets/image/default.svg", // Provide a default SVG path
                                        height: 50,
                                        width: 50,
                                        color: NoteColors.blackbold,
                                      ),
                                      SizedBox(height: 15.sp,),
                                      Center(
                                          child: Text(item["name"]!,style: TextStyle(
                                            color: controller.current.value == index ?  NoteColors.white: NoteColors.blackbold,
                                            fontSize: 15.sp,fontWeight: FontWeight.w400,
                                            fontFamily: "poppins_regular",
                                          ),)),
                                    ],
                                  ),
                                 ),
                               ),
                            ),
                          )
                    );
                  }),
            Spacer(),
            MyButton(
              onPressed: () {
                context.openScreen(DatePicker());
              },
              text: "Continue",fontFamily: "pro_bold",fontSize: 17.sp,
              paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
              textColor:  NoteColors.white,
              buttonColor:  NoteColors.bluePrimery,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.sp),
              ),
            ).marginSymmetric(horizontal: 24.sp,vertical: 20.sp)
          ],
        ),
      ),
    );
  }
}
// controller.current.value = index;
// MyListTile(
//                             title: Text(item["name"] ?? "default name",style: TextStyle(fontFamily: "pro_regular",fontSize: 16.sp,fontWeight: FontWeight.w600,color: controller.current.value == index ? NoteColors.bluePrimery : NoteColors.blackbold.withValues(alpha: 0.80)),),
//                             trailing: controller.current.value == index ? Icon(Icons.check_circle_rounded ,color: NoteColors.bluePrimery,) : Icon(Icons.radio_button_off_outlined,color: NoteColors.white,),
//                           ),