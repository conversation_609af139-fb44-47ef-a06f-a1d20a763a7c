import 'package:accounting_app/accounting_ui/authentication/auth/sign_up_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:sizer/sizer.dart';

import 'login_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: NoteColors.background,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      SizedBox(height: AppTheme.spacingXl),

                      // Professional Hero Section
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildHeroSection(),
                      ),

                      SizedBox(height: AppTheme.spacingXxl),

                      // Feature Cards
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildFeatureCards(),
                        ),
                      ),

                      SizedBox(height: AppTheme.spacingXxl),

                      // Content Section
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildContentSection(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Bottom Action Section
              SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildBottomActions(),
                ),
              ),

              SizedBox(height: AppTheme.spacingLg),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection() {
    return Column(
      children: [
        // App Icon with Professional Styling
        Container(
          width: 100.sp,
          height: 100.sp,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                NoteColors.primary,
                NoteColors.primaryVariant,
              ],
            ),
            borderRadius: BorderRadius.circular(AppTheme.radiusXl),
            boxShadow: [
              BoxShadow(
                color: NoteColors.primary.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Icon(
            LucideIcons.calculator,
            size: 50.sp,
            color: NoteColors.onPrimary,
          ),
        ),

        SizedBox(height: AppTheme.spacingLg),

        // App Title
        Text(
          'Accounting Pro',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 32.sp,
            fontWeight: FontWeight.w700,
            fontFamily: 'inter_bold',
            letterSpacing: -0.5,
          ),
        ),

        SizedBox(height: AppTheme.spacingSm),

        // Subtitle
        Text(
          'Professional Financial Management',
          style: TextStyle(
            color: NoteColors.textSecondary,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter_regular',
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureCards() {
    final features = [
      {
        'icon': LucideIcons.trendingUp,
        'title': 'Track Expenses',
        'description': 'Monitor your spending patterns',
      },
      {
        'icon': LucideIcons.fileText,
        'title': 'Generate Reports',
        'description': 'Professional financial reports',
      },
      {
        'icon': LucideIcons.shield,
        'title': 'Secure & Private',
        'description': 'Your data is protected',
      },
    ];

    return Row(
      children: features.map((feature) {
        return Expanded(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: AppTheme.spacingXs),
            padding: EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: NoteColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              border: Border.all(
                color: NoteColors.borderLight,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: NoteColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  width: 40.sp,
                  height: 40.sp,
                  decoration: BoxDecoration(
                    color: NoteColors.primaryContainer,
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  ),
                  child: Icon(
                    feature['icon'] as IconData,
                    size: AppTheme.iconSm,
                    color: NoteColors.primary,
                  ),
                ),
                SizedBox(height: AppTheme.spacingSm),
                Text(
                  feature['title'] as String,
                  style: TextStyle(
                    color: NoteColors.textPrimary,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'inter_semibold',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: AppTheme.spacingXs),
                Text(
                  feature['description'] as String,
                  style: TextStyle(
                    color: NoteColors.textTertiary,
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'inter_regular',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildContentSection() {
    return Column(
      children: [
        Text(
          'Welcome to Professional\nAccounting',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 24.sp,
            fontWeight: FontWeight.w700,
            fontFamily: 'inter_bold',
            height: 1.3,
            letterSpacing: -0.3,
          ),
        ),

        SizedBox(height: AppTheme.spacingMd),

        Text(
          'Streamline your financial management with our comprehensive accounting solution. Track expenses, generate reports, and maintain professional records with ease.',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: NoteColors.textSecondary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter_regular',
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Column(
      children: [
        // Get Started Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              context.openScreen(const LoginScreen());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: NoteColors.primary,
              foregroundColor: NoteColors.onPrimary,
              elevation: 2,
              shadowColor: NoteColors.primary.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              ),
              padding: EdgeInsets.symmetric(vertical: 18.sp),
            ),
            child: Text(
              'Get Started',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: 'inter_semibold',
              ),
            ),
          ),
        ),

        SizedBox(height: AppTheme.spacingMd),

        // Sign Up Link
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Don't have an account? ",
              style: TextStyle(
                color: NoteColors.textSecondary,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                fontFamily: 'inter_regular',
              ),
            ),
            TextButton(
              onPressed: () {
                context.openScreen(const SignUpScreen());
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingSm),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                'Sign Up',
                style: TextStyle(
                  color: NoteColors.primary,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'inter_semibold',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
