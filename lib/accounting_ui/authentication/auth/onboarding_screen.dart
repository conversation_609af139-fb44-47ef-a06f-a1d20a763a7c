import 'package:accounting_app/accounting_ui/authentication/auth/sign_up_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'login_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              NoteColors.white,
              NoteColors.f9fa,
              NoteColors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.sp),
              child: Column(
                children: [
                  SizedBox(height: 30.sp),
                  
                  // Hero Image Section with modern styling
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Container(
                      height: 45.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.sp),
                        boxShadow: [
                          BoxShadow(
                            color: NoteColors.bluePrimery.withValues(alpha: 0.1),
                            blurRadius: 30,
                            offset: const Offset(0, 15),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(30.sp),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                NoteColors.bluePrimery.withValues(alpha: 0.05),
                                NoteColors.d9d9.withValues(alpha: 0.3),
                              ],
                            ),
                          ),
                          child: Image.asset(
                            "assets/png/onboard.png",
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 40.sp),

                  // Content Section with animations
                  SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          // Main Title with improved typography
                          Text(
                            "Welcome to Your\nAccounting Journey",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: NoteColors.blackbold,
                              fontSize: 28.sp,
                              fontWeight: FontWeight.w700,
                              fontFamily: "gen_semibold",
                              height: 1.2,
                              letterSpacing: -0.5,
                            ),
                          ),

                          SizedBox(height: 16.sp),

                          // Subtitle with better spacing
                          Text(
                            "Manage your finances with ease and precision.\nTrack expenses, generate reports, and stay organized.",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: NoteColors.blackbold.withValues(alpha: 0.7),
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              fontFamily: "gen_regular",
                              height: 1.5,
                              letterSpacing: 0.2,
                            ),
                          ),

                          SizedBox(height: 35.sp),

                          // Modern Progress Indicator
                          _buildModernProgressIndicator(),

                          SizedBox(height: 40.sp),

                          // Enhanced Get Started Button
                          _buildGetStartedButton(),

                          SizedBox(height: 20.sp),

                          // Sign up section with better styling
                          _buildSignUpSection(),

                          SizedBox(height: 30.sp),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernProgressIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Active indicator
        Container(
          height: 6.sp,
          width: 30.sp,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                NoteColors.bluePrimery,
                NoteColors.bluePrimery.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(10.sp),
            boxShadow: [
              BoxShadow(
                color: NoteColors.bluePrimery.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        SizedBox(width: 8.sp),
        // Inactive indicators
        ...List.generate(2, (index) => Container(
          margin: EdgeInsets.only(right: 8.sp),
          height: 6.sp,
          width: 6.sp,
          decoration: BoxDecoration(
            color: NoteColors.bluePrimery.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(10.sp),
          ),
        )),
      ],
    );
  }

  Widget _buildGetStartedButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.sp),
        boxShadow: [
          BoxShadow(
            color: NoteColors.bluePrimery.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: MyButton(
        onPressed: () {
          context.openScreen(LoginScreen());
        },
        text: "Get Started",
        fontFamily: "pro_medium",
        fontSize: 18.sp,
        paddingVertical: EdgeInsets.symmetric(vertical: 20.sp),
        textColor: NoteColors.white,
        buttonColor: NoteColors.bluePrimery,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.sp),
        ),
      ),
    );
  }

  Widget _buildSignUpSection() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 15.sp, horizontal: 20.sp),
      decoration: BoxDecoration(
        color: NoteColors.f9fa,
        borderRadius: BorderRadius.circular(15.sp),
        border: Border.all(
          color: NoteColors.e5e5.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Don't have an account?",
            style: TextStyle(
              color: NoteColors.blackbold.withValues(alpha: 0.6),
              fontSize: 15.sp,
              fontWeight: FontWeight.w400,
              fontFamily: "gen_regular",
            ),
          ),
          TextButton(
            onPressed: () {
              context.openScreen(SignUpScreen());
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 4.sp),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              "Create one",
              style: TextStyle(
                color: NoteColors.bluePrimery,
                fontSize: 15.sp,
                fontWeight: FontWeight.w600,
                fontFamily: "gen_medium",
                decoration: TextDecoration.underline,
                decorationColor: NoteColors.bluePrimery.withValues(alpha: 0.5),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
