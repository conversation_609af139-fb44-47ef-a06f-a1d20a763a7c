import 'package:accounting_app/accounting_ui/authentication/auth/sign_up_screen.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:sizer/sizer.dart';

import '../forgot_password/forgot_password.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final LoginController controller = Get.put(LoginController());

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: NoteColors.background,
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: AppTheme.spacingXl),

                // Back Button
                _buildBackButton(context),

                SizedBox(height: AppTheme.spacingXl),

                // Header Section
                _buildHeader(),

                SizedBox(height: AppTheme.spacingXxl),

                // Login Form
                _buildLoginForm(controller),

                SizedBox(height: AppTheme.spacingLg),

                // Sign Up Link
                _buildSignUpLink(context),

                SizedBox(height: AppTheme.spacingXl),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: NoteColors.surface,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        border: Border.all(
          color: NoteColors.borderLight,
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          LucideIcons.arrowLeft,
          size: AppTheme.iconSm,
        ),
        style: IconButton.styleFrom(
          foregroundColor: NoteColors.textPrimary,
          padding: EdgeInsets.all(AppTheme.spacingMd),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome Back',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 32.sp,
            fontWeight: FontWeight.w700,
            fontFamily: 'inter_bold',
            letterSpacing: -0.5,
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        Text(
          'Sign in to your account to continue',
          style: TextStyle(
            color: NoteColors.textSecondary,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter_regular',
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm(LoginController controller) {
    return Column(
      children: [
        // Email Field
        _buildEmailField(controller),

        SizedBox(height: AppTheme.spacingLg),

        // Password Field
        _buildPasswordField(controller),

        SizedBox(height: AppTheme.spacingMd),

        // Forgot Password Link
        _buildForgotPasswordLink(),

        SizedBox(height: AppTheme.spacingXl),

        // Login Button
        _buildLoginButton(controller),
      ],
    );
  }

  Widget _buildEmailField(LoginController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email Address',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        TextFormField(
          controller: controller.emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onChanged: controller.validateEmail,
          decoration: InputDecoration(
            hintText: 'Enter your email address',
            prefixIcon: const Icon(
              LucideIcons.mail,
              size: AppTheme.iconSm,
            ),
            suffixIcon: Obx(() => controller.isEmailValid.value
                ? const Icon(
                    LucideIcons.check,
                    color: NoteColors.success,
                    size: AppTheme.iconSm,
                  )
                : const SizedBox.shrink()),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField(LoginController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        Obx(() => TextFormField(
              controller: controller.passwordController,
              obscureText: controller.obscurePassword.value,
              textInputAction: TextInputAction.done,
              onChanged: controller.validatePassword,
              decoration: InputDecoration(
                hintText: 'Enter your password',
                prefixIcon: const Icon(
                  LucideIcons.lock,
                  size: AppTheme.iconSm,
                ),
                suffixIcon: IconButton(
                  onPressed: controller.togglePasswordVisibility,
                  icon: Icon(
                    controller.obscurePassword.value
                        ? LucideIcons.eyeOff
                        : LucideIcons.eye,
                    size: AppTheme.iconSm,
                  ),
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildForgotPasswordLink() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () => Get.to(() => const ForgotPassword()),
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSm,
            vertical: AppTheme.spacingXs,
          ),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: Text(
          'Forgot Password?',
          style: TextStyle(
            color: NoteColors.primary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            fontFamily: 'inter_medium',
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton(LoginController controller) {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.isFormValid && !controller.isLoading.value
                ? controller.login
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: NoteColors.primary,
              foregroundColor: NoteColors.onPrimary,
              disabledBackgroundColor: NoteColors.interactiveDisabled,
              elevation: controller.isFormValid ? 2 : 0,
              shadowColor: NoteColors.primary.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              ),
              padding: EdgeInsets.symmetric(vertical: 18.sp),
            ),
            child: controller.isLoading.value
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        NoteColors.onPrimary,
                      ),
                    ),
                  )
                : Text(
                    'Sign In',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'inter_semibold',
                    ),
                  ),
          ),
        ));
  }

  Widget _buildSignUpLink(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: TextStyle(
            color: NoteColors.textSecondary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter_regular',
          ),
        ),
        TextButton(
          onPressed: () => Get.to(() => const SignUpScreen()),
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingSm),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            'Sign Up',
            style: TextStyle(
              color: NoteColors.primary,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              fontFamily: 'inter_semibold',
            ),
          ),
        ),
      ],
    );
  }
}

class LoginController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  
  final RxBool isEmailValid = false.obs;
  final RxBool isPasswordValid = false.obs;
  final RxBool isLoading = false.obs;
  final RxBool obscurePassword = true.obs;

  bool get isFormValid => isEmailValid.value && isPasswordValid.value;

  void validateEmail(String email) {
    isEmailValid.value = GetUtils.isEmail(email);
  }

  void validatePassword(String password) {
    isPasswordValid.value = password.length >= 6;
  }

  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  Future<void> login() async {
    if (!isFormValid) return;

    isLoading.value = true;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Navigate to main screen
      Get.offAll(() => NavigationScreen());
    } catch (e) {
      Get.snackbar(
        'Error',
        'Login failed. Please try again.',
        backgroundColor: NoteColors.errorLight,
        colorText: NoteColors.error,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}
