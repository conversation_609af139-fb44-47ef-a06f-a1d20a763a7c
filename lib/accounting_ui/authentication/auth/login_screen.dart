import 'package:accounting_app/accounting_ui/authentication/auth/sign_up_screen.dart';
import 'package:accounting_app/accounting_ui/main_screens/navigaion_screen.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:accounting_app/my_widget/my_email_textfield.dart';
import 'package:accounting_app/my_widget/my_password.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import '../forgot_password/forgot_password.dart';

class LoginScreen extends StatelessWidget {
  // Reactive variables using GetX
  final TextEditingController emailController = TextEditingController();

  final TextEditingController passwordController = TextEditingController();
  final RxBool isEmailValid = false.obs;
  final RxBool isPasswordValid = false.obs;
  final RxBool isLoading = false.obs;
  final RxBool obscurePassword = true.obs;
  LoginScreen({super.key});

  // Computed property for form validation
  bool get isFormValid => isEmailValid.value && isPasswordValid.value;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 40.sp),

                // Header Section
                _buildHeader(),

                SizedBox(height: 35.sp),

                // Login Form Card
                _buildLoginFormCard(),

                SizedBox(height: 20.sp),

                // Social Login Section
                _buildSocialLoginSection(),

                SizedBox(height: 25.sp),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Email Address",
          style: TextStyle(
            color: NoteColors.blackbold,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: "gen_medium",
          ),
        ),
        SizedBox(height: 8.sp),
        Container(
          decoration: BoxDecoration(
            color: NoteColors.f9fa,
            borderRadius: BorderRadius.circular(12.sp),
            border: Border.all(
              color: NoteColors.e5e5.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: MyEmail(
            controller: emailController,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.sp, vertical: 16.sp),
            inValid: (isValid) => isEmailValid.value = isValid,
            keyboardType: TextInputType.emailAddress,
            fillColor: Colors.transparent,
            hintText: "Enter your email address",
            errorText: "Please enter a valid email",
          ),
        ),
      ],
    );
  }

  Widget _buildForgotPasswordLink() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () => Get.to(() => ForgotPassword()),
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 8.sp),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: Text(
          "Forgot Password?",
          style: TextStyle(
            color: NoteColors.bluePrimery,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: "gen_medium",
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // App Logo - smaller and more professional
        Container(
          height: 40.sp,
          width: 40.sp,
          decoration: BoxDecoration(
            color: NoteColors.bluePrimery.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(10.sp),
          ),
          child: Icon(
            Icons.account_balance_wallet_outlined,
            color: NoteColors.bluePrimery,
            size: 20.sp,
          ),
        ),

        SizedBox(height: 20.sp),

        Text(
          "Welcome Back",
          style: TextStyle(
            color: NoteColors.blackbold,
            fontSize: 28.sp,
            fontWeight: FontWeight.w600,
            fontFamily: "gen_semibold",
            letterSpacing: -0.3,
          ),
        ),

        SizedBox(height: 6.sp),

        Text(
          "Sign in to your accounting dashboard",
          style: TextStyle(
            color: NoteColors.blackbold.withValues(alpha: 0.6),
            fontSize: 15.sp,
            fontWeight: FontWeight.w400,
            fontFamily: "gen_regular",
            height: 1.3,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return Obx(() => Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.sp),
            boxShadow: isFormValid
                ? [
                    BoxShadow(
                      color: NoteColors.bluePrimery.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                      spreadRadius: 0,
                    ),
                  ]
                : [],
          ),
          child: MyButton(
            onPressed: isLoading.value ? null : _handleLogin,
            text: isLoading.value ? "Signing In..." : "Sign In",
            fontFamily: "pro_bold",
            fontSize: 15.sp,
            paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
            textColor: NoteColors.white,
            buttonColor: isFormValid ? NoteColors.bluePrimery : NoteColors.d9d9,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.sp),
            ),
          ),
        ));
  }

  Widget _buildLoginFormCard() {
    return Container(
      padding: EdgeInsets.all(24.sp),
      decoration: BoxDecoration(
        color: NoteColors.white,
        borderRadius: BorderRadius.circular(16.sp),
        boxShadow: [
          BoxShadow(
            color: NoteColors.blackbold.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: NoteColors.e5e5.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Email Field
          _buildEmailField(),

          SizedBox(height: 20.sp),

          // Password Field
          _buildPasswordField(),

          SizedBox(height: 16.sp),

          // Forgot Password Link
          _buildForgotPasswordLink(),

          SizedBox(height: 32.sp),

          // Login Button
          _buildLoginButton(),

          SizedBox(height: 24.sp),

          // Register Link
          _buildRegisterLink(),
        ],
      ),
    );
  }

  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Password",
          style: TextStyle(
            color: NoteColors.blackbold,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: "gen_medium",
          ),
        ),
        SizedBox(height: 8.sp),
        Container(
          decoration: BoxDecoration(
            color: NoteColors.f9fa,
            borderRadius: BorderRadius.circular(12.sp),
            border: Border.all(
              color: NoteColors.e5e5.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: MyPassword(
            controller: passwordController,
            inValid: (isValid) => isPasswordValid.value = isValid,
            hintText: "Enter your password",
            errortext: "Password is required",
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: TextStyle(
            color: NoteColors.blackbold.withValues(alpha: 0.6),
            fontSize: 15.sp,
            fontWeight: FontWeight.w400,
            fontFamily: "gen_regular",
          ),
        ),
        TextButton(
          onPressed: () => Get.to(() => SignUpScreen()),
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 4.sp),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            "Sign Up",
            style: TextStyle(
              color: NoteColors.bluePrimery,
              fontSize: 15.sp,
              fontWeight: FontWeight.w700,
              fontFamily: "gen_medium",
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required Widget icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 14.sp, horizontal: 16.sp),
        decoration: BoxDecoration(
          color: NoteColors.white,
          borderRadius: BorderRadius.circular(12.sp),
          border: Border.all(
            color: NoteColors.e5e5.withValues(alpha: 0.4),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: NoteColors.blackbold.withValues(alpha: 0.03),
              blurRadius: 6,
              offset: const Offset(0, 1),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            SizedBox(width: 10.sp),
            Text(
              text,
              style: TextStyle(
                color: NoteColors.blackbold,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                fontFamily: "gen_medium",
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialLoginSection() {
    return Column(
      children: [
        // Divider with OR text
        Row(
          children: [
            Expanded(
              child: Container(
                height: 1,
                color: NoteColors.e5e5.withValues(alpha: 0.6),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.sp),
              child: Text(
                "OR",
                style: TextStyle(
                  color: NoteColors.blackbold.withValues(alpha: 0.5),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: "gen_medium",
                ),
              ),
            ),
            Expanded(
              child: Container(
                height: 1,
                color: NoteColors.e5e5.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),

        SizedBox(height: 24.sp),

        // Social Login Buttons
        _buildSocialButton(
          icon: SvgPicture.asset("assets/image/google.svg",
              height: 18, width: 18),
          text: "Continue with Google",
          onTap: _handleGoogleLogin,
        ),

        SizedBox(height: 12.sp),

        _buildSocialButton(
          icon: SvgPicture.asset("assets/png/apple.svg", height: 18, width: 18),
          text: "Continue with Apple",
          onTap: _handleAppleLogin,
        ),
      ],
    );
  }

  // Apple login handler
  void _handleAppleLogin() {
    Get.snackbar(
      "Apple Login",
      "Apple login integration coming soon",
      backgroundColor: NoteColors.blackbold,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
      margin: EdgeInsets.all(16.sp),
      borderRadius: 12.sp,
      duration: const Duration(seconds: 2),
    );
  }

  // Google login handler
  void _handleGoogleLogin() {
    Get.snackbar(
      "Google Login",
      "Google login integration coming soon",
      backgroundColor: NoteColors.bluePrimery,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
      margin: EdgeInsets.all(16.sp),
      borderRadius: 12.sp,
      duration: const Duration(seconds: 2),
    );
  }

  // Login handler with loading state
  void _handleLogin() async {
    if (!isFormValid) {
      Get.snackbar(
        "Validation Error",
        "Please fill in all fields correctly",
        backgroundColor: NoteColors.reds,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        margin: EdgeInsets.all(16.sp),
        borderRadius: 12.sp,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      isLoading.value = true;

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Navigate to main screen
      Get.offAll(() => NavigaionScreen());

      // Show success message
      Get.snackbar(
        "Success",
        "Welcome back!",
        backgroundColor: NoteColors.greenp,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        margin: EdgeInsets.all(16.sp),
        borderRadius: 12.sp,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        "Login Failed",
        "Please check your credentials and try again",
        backgroundColor: NoteColors.reds,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        margin: EdgeInsets.all(16.sp),
        borderRadius: 12.sp,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }
}
