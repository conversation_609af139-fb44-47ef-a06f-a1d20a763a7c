import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:accounting_app/my_widget/my_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import 'gender_screen.dart';

class HomeLand extends StatelessWidget {
  HomeLand({super.key});
  final RxList<Map<String, dynamic>> founds = List<Map<String, dynamic>>.from(
      countryList).obs;
  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      founds.assignAll(List.from(countryList));
    } else {
      founds.assignAll(
        countryList.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    return Scaffold(
      backgroundColor: NoteColors.white,
      appBar: AppBar(
          centerTitle: true,
          backgroundColor: NoteColors.white,
          surfaceTintColor: NoteColors.white,
          title: Stack(
            children: [
              Container(
                height: 8, width: 180,
                decoration: BoxDecoration(
                  color: NoteColors.backgrond.withValues(alpha: 0.10),
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
              Container(
                height: 8, width: 60,
                decoration: BoxDecoration(
                  color: NoteColors.bluePrimery,
                  borderRadius: BorderRadius.circular(10.sp),
                ),
              ),
            ],
          )
      ),
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 15.sp, right: 32.sp, top: 20.sp),
              child: Text("What’s your Childhood Homeland?",
                textAlign: TextAlign.start,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 18.sp, fontWeight: FontWeight.w700,
                  fontFamily: "poppins_bold",
                ),).marginSymmetric(horizontal: 15.sp),
            ),
            Padding(
              padding: EdgeInsets.only(left: 15.sp, right: 20.sp),
              child: TextField(
                onChanged: _runFilter,
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: 15.sp, vertical: 15.sp),
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: NoteColors.c6c6c6,),
                  ),
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(14.sp),
                    child: SvgPicture.asset(
                      "assets/image/search_land.svg", height: 13, width: 13,),
                  ),
                  hintText: "Search Countries",
                  hintStyle: TextStyle(
                    color: NoteColors.gry.withValues(alpha: .50),
                    fontFamily: "pro_semibold",
                    fontWeight: FontWeight.w600,
                    fontSize: 16.sp,
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: NoteColors.c6c6c6,),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: NoteColors.c6c6c6,),
                  ),
                ),

              ),
            ),
            SizedBox(height: 14.sp,),
            Expanded(
              child: Obx(() {
                if (founds.isEmpty) {
                  return Center(
                    child: Text(
                      "No results found",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: NoteColors.blackbold.withOpacity(0.6),
                      ),
                    ),
                  );
                } else {
                  return ListView.builder(
                      scrollDirection: Axis.vertical,
                      shrinkWrap: true,
                      physics: BouncingScrollPhysics(),
                      itemCount: founds.length,
                      itemBuilder: (context, index) {
                        var item = founds[index];
                        return Padding(
                          padding: EdgeInsets.only(left: 16.sp, right: 16.sp),
                          child: GestureDetector(
                            onTap: () {
                              controller.current.value = index;
                            },
                            child: Obx(() =>
                                MyListTile(
                                  title: Text(item["name"] ?? "default name",
                                    style: TextStyle(
                                        fontFamily: controller.current.value ==
                                            index ? "pro_bold" : "pro_regular",
                                        fontSize: 17.sp,
                                        fontWeight: FontWeight.w600,
                                        color: controller.current.value == index
                                            ? NoteColors.bluePrimery
                                            : NoteColors.blackbold.withValues(
                                            alpha: 0.60)),),
                                  trailing: controller.current.value == index ?
                                  GestureDetector(
                                      onTap: () {
                                        context.openScreen(GenderScreen());
                                      },
                                      child: Icon(Icons.check_circle_rounded,
                                        color: NoteColors.bluePrimery,)) : Icon(
                                    Icons.radio_button_off_outlined,
                                    color: NoteColors.white,),
                                ),
                            ),
                          ),
                        );
                      });
                } }),
            ),
            SizedBox(height: 20.sp),
          ],
        ),
      ),
    );
  }
}
//    enabledBorder: OutlineInputBorder(
//                         borderRadius: BorderRadius.circular(8), // Optional: Set border radius
//                         borderSide: BorderSide(color: Colors.blue),
//                       ),
// MyButton(
//   onPressed:  (){
//     context.openScreen(GenderScreen());
//   },
//   text: "Continue",fontFamily: "pro_bold",fontSize: 17.sp,
//   paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
//   textColor:  NoteColors.white,
//   buttonColor:  NoteColors.bluePrimery,
//   shape: RoundedRectangleBorder(
//     borderRadius: BorderRadius.circular(15.sp),
//   ),
// ).marginSymmetric(horizontal: 20.sp,),