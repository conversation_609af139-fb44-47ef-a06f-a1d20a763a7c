import 'package:accounting_app/accounting_ui/authentication/auth/upload_image.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:accounting_app/my_widget/my_email_textfield.dart';
import 'package:accounting_app/my_widget/my_password.dart';
import 'package:accounting_app/my_widget/my_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import 'login_screen.dart';
class SignUpScreen extends StatefulWidget {
   const SignUpScreen({super.key});
  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}
class _SignUpScreenState extends State<SignUpScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isField1Valid = false;
  bool _isField2Valid = false;
  bool _isField3Valid = false;
  void _updateField(int fieldIndex, bool isValid) {
    setState(() {
      if (fieldIndex == 1) _isField1Valid = isValid;
      if (fieldIndex == 2) _isField2Valid = isValid;
      if (fieldIndex == 3) _isField3Valid = isValid;
    });
  }
  bool get _allFieldsValid => _isField1Valid && _isField2Valid && _isField3Valid;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 30.sp,),
              Center(
                child: Text("Call Center",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: NoteColors.bluePrimery,
                    fontSize: 26.sp,fontWeight: FontWeight.w400,
                    fontFamily: "poppins_regular",
                  ),).marginSymmetric(horizontal: 20.sp),
              ),
              Center(
                child: Text(
                  "Find best worker for yourself!",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: NoteColors.blackbold.withValues(alpha: 0.50),
                    fontSize: 16.sp,fontWeight: FontWeight.w400,
                    fontFamily: "poppins_regular",
                  ),).marginSymmetric(horizontal: 20.sp),
              ),
              Padding(
                padding:  EdgeInsets.only(left: 15.sp,right: 15.sp,top: 24.sp),
                child: MyTextField(
                  contentPadding: EdgeInsets.symmetric(horizontal: 18.sp,vertical: 18.sp),
                  controller: _nameController,
                  inValid: (isValid) => _updateField(1, isValid),
                  keyboardType: TextInputType.name,
                  fillColor: NoteColors.white,
                  hintText: "Enter your name",
                  errorText: "invalid name",
          
                ),
              ),
              Padding(
                padding:  EdgeInsets.only(left: 15.sp,right: 15.sp,top: 15.sp),
                child: MyEmail(
                  controller: _emailController,
                  contentPadding: EdgeInsets.symmetric(horizontal: 18.sp,vertical: 18.sp),
                  inValid: (isValid ) => _updateField(2, isValid),
                  keyboardType: TextInputType.emailAddress,
                  fillColor: NoteColors.white,
                  hintText: "Enter your Email",
                  errorText: "invalid Email",
                ),
              ),
              Padding(
                padding:  EdgeInsets.only(left: 15.sp,right: 15.sp,top: 15.sp),
                child: MyPassword(
                  controller: _passwordController,
                    inValid: (isValid) => _updateField(3, isValid),
                  hintText: "Enter your Password",
                  errortext: "invalid password",
          
                ),
              ),
              SizedBox(height: 35.sp,),
              MyButton(
                onPressed:  (){
                  if(_allFieldsValid){
                    context.openScreen(UploadImage());
                  } else {
                       Get.snackbar(
                      "Error",
                      "You must agree to the Terms of Services",
                      backgroundColor: NoteColors.bluePrimery,
                      colorText: Colors.white,
                    );
                  }
                },
                text: "Register",fontFamily: "pro_bold",fontSize: 17.sp,
                paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
                textColor:  NoteColors.white,
                buttonColor: _allFieldsValid ? NoteColors.bluePrimery : NoteColors.d9d9,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.sp),
                ),
              ).marginSymmetric(horizontal: 20.sp,),
              TextButton(
                onPressed: (){
                   context.openScreen(LoginScreen());
                },
                child: Text("Login",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: NoteColors.blackbold,
                  fontSize: 16.sp,fontWeight: FontWeight.w400,
                  fontFamily: "gen_medium",
                ),),
              ),
              SizedBox(height: 20.sp),
              Container(
                height: 35,width: 360,
                decoration: BoxDecoration(
                color: NoteColors.white,
              ),
                child:  Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.only( left: 15.sp, right: 15.sp,top: 12.sp),
                    child: Divider(
                      color: NoteColors.f8f8f8.withValues(alpha: .20),
                    ),
                  ),
                  Positioned(
                    left: 53.5.sp,top: 8.sp,
                    child: Container(
                      height: 26,
                      width: 46,
                      decoration: BoxDecoration(
                        color: NoteColors.f2f2,
                        borderRadius: BorderRadius.circular(20.sp),
                      ),
                      child: Center(
                          child: Text(
                            "OR",
                            style: TextStyle(
                                color: NoteColors.f8f8f8,
                                fontWeight: FontWeight.w600,
                                fontSize: 15.sp,
                                fontFamily: "pro_medium"),
                          )),
                    ),
                  ),
                ],
              ),),
              SizedBox(height: 15.sp),
              _buildContonue(SvgPicture.asset("assets/image/google.svg",height: 23,width: 23,), "Continue with Google"),
              _buildContonue(SvgPicture.asset("assets/png/apple.svg",height: 23,width: 23,), "Continue with Apple")
            ],
          ),
        ),
      ),
    );
  }
  Widget _buildContonue(Widget? icon,String text,){
    return  Padding(
      padding:  EdgeInsets.only(top: 14.sp),
      child: Container(
        height: 48,width: 330,
        decoration: BoxDecoration(
          color: NoteColors.f2f2,
          borderRadius: BorderRadius.circular(12.sp),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon ?? SizedBox(),
            SizedBox(width: 20.sp),
            Text(
              text,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: NoteColors.b3b,
                fontSize: 16.sp,fontWeight: FontWeight.w400,
                fontFamily: "pro_regular",
              ),).marginOnly(right: 26.sp)
          ],
        ),
      ),
    );
  }
}
///
// Padding(
//   padding:  EdgeInsets.only(top: 28.sp),
//   child: Container(
//     height: 48,width: 336,
//   decoration: BoxDecoration(
//     color: NoteColors.f2f2,
//     borderRadius: BorderRadius.circular(10.sp),
//
//   ),
//     child: Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         SvgPicture.asset("assets/image/google.svg",height: 23,width: 23,),
//         SizedBox(width: 20.sp,),
//         Text(
//           "Continue with Google",
//           textAlign: TextAlign.center,
//           style: TextStyle(
//             color: NoteColors.blackbold,
//             fontSize: 15.sp,fontWeight: FontWeight.w400,
//             fontFamily: "pro_medium",
//           ),).marginOnly(right: 26.sp)
//       ],
//     ),
//   ),
// ),
// Padding(
//   padding:  EdgeInsets.only(top: 14.sp),
//   child: Container(
//     height: 48,width: 336,
//     decoration: BoxDecoration(
//       color: NoteColors.f2f2,
//       borderRadius: BorderRadius.circular(10.sp),
//     ),
//     child: Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         SvgPicture.asset("assets/png/apple.svg",height: 23,width: 23,),
//         SizedBox(width: 20.sp,),
//         Text(
//           "Continue with Apple",
//           textAlign: TextAlign.center,
//           style: TextStyle(
//             color: NoteColors.blackbold,
//             fontSize: 15.sp,fontWeight: FontWeight.w400,
//             fontFamily: "pro_medium",
//           ),).marginOnly(right: 26.sp)
//       ],
//     ),
//   ),
// ),