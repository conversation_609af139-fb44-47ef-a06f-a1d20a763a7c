import 'package:accounting_app/accounting_ui/authentication/auth/upload_image.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:sizer/sizer.dart';
import 'login_screen.dart';

class SignUpScreen extends StatelessWidget {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final SignUpController controller = Get.put(SignUpController());

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: NoteColors.background,
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: AppTheme.spacingXl),

                // Back Button
                _buildBackButton(context),

                SizedBox(height: AppTheme.spacingXl),

                // Header Section
                _buildHeader(),

                SizedBox(height: AppTheme.spacingXxl),

                // Sign Up Form
                _buildSignUpForm(controller),

                SizedBox(height: AppTheme.spacingLg),

                // Login Link
                _buildLoginLink(context),

                SizedBox(height: AppTheme.spacingXl),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: NoteColors.surface,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        border: Border.all(
          color: NoteColors.borderLight,
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          LucideIcons.arrowLeft,
          size: AppTheme.iconSm,
        ),
        style: IconButton.styleFrom(
          foregroundColor: NoteColors.textPrimary,
          padding: EdgeInsets.all(AppTheme.spacingMd),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Create Account',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 32.sp,
            fontWeight: FontWeight.w700,
            fontFamily: 'inter_bold',
            letterSpacing: -0.5,
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        Text(
          'Join us to start managing your finances professionally',
          style: TextStyle(
            color: NoteColors.textSecondary,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter_regular',
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpForm(SignUpController controller) {
    return Column(
      children: [
        // Full Name Field
        _buildNameField(controller),

        SizedBox(height: AppTheme.spacingLg),

        // Email Field
        _buildEmailField(controller),

        SizedBox(height: AppTheme.spacingLg),

        // Password Field
        _buildPasswordField(controller),

        SizedBox(height: AppTheme.spacingLg),

        // Confirm Password Field
        _buildConfirmPasswordField(controller),

        SizedBox(height: AppTheme.spacingMd),

        // Terms and Conditions
        _buildTermsCheckbox(controller),

        SizedBox(height: AppTheme.spacingXl),

        // Sign Up Button
        _buildSignUpButton(controller),
      ],
    );
  }

  Widget _buildNameField(SignUpController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Full Name',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        TextFormField(
          controller: controller.nameController,
          textInputAction: TextInputAction.next,
          textCapitalization: TextCapitalization.words,
          onChanged: controller.validateName,
          decoration: InputDecoration(
            hintText: 'Enter your full name',
            prefixIcon: const Icon(
              LucideIcons.user,
              size: AppTheme.iconSm,
            ),
            suffixIcon: Obx(() => controller.isNameValid.value
                ? const Icon(
                    LucideIcons.check,
                    color: NoteColors.success,
                    size: AppTheme.iconSm,
                  )
                : const SizedBox.shrink()),
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField(SignUpController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email Address',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        TextFormField(
          controller: controller.emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onChanged: controller.validateEmail,
          decoration: InputDecoration(
            hintText: 'Enter your email address',
            prefixIcon: const Icon(
              LucideIcons.mail,
              size: AppTheme.iconSm,
            ),
            suffixIcon: Obx(() => controller.isEmailValid.value
                ? const Icon(
                    LucideIcons.check,
                    color: NoteColors.success,
                    size: AppTheme.iconSm,
                  )
                : const SizedBox.shrink()),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField(SignUpController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        Obx(() => TextFormField(
              controller: controller.passwordController,
              obscureText: controller.obscurePassword.value,
              textInputAction: TextInputAction.next,
              onChanged: controller.validatePassword,
              decoration: InputDecoration(
                hintText: 'Create a strong password',
                prefixIcon: const Icon(
                  LucideIcons.lock,
                  size: AppTheme.iconSm,
                ),
                suffixIcon: IconButton(
                  onPressed: controller.togglePasswordVisibility,
                  icon: Icon(
                    controller.obscurePassword.value
                        ? LucideIcons.eyeOff
                        : LucideIcons.eye,
                    size: AppTheme.iconSm,
                  ),
                ),
              ),
            )),
        SizedBox(height: AppTheme.spacingXs),
        Obx(() => Text(
              'Password must be at least 8 characters',
              style: TextStyle(
                color: controller.isPasswordValid.value
                    ? NoteColors.success
                    : NoteColors.textTertiary,
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                fontFamily: 'inter_regular',
              ),
            )),
      ],
    );
  }

  Widget _buildConfirmPasswordField(SignUpController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Confirm Password',
          style: TextStyle(
            color: NoteColors.textPrimary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
        SizedBox(height: AppTheme.spacingSm),
        Obx(() => TextFormField(
              controller: controller.confirmPasswordController,
              obscureText: controller.obscureConfirmPassword.value,
              textInputAction: TextInputAction.done,
              onChanged: controller.validateConfirmPassword,
              decoration: InputDecoration(
                hintText: 'Confirm your password',
                prefixIcon: const Icon(
                  LucideIcons.lock,
                  size: AppTheme.iconSm,
                ),
                suffixIcon: IconButton(
                  onPressed: controller.toggleConfirmPasswordVisibility,
                  icon: Icon(
                    controller.obscureConfirmPassword.value
                        ? LucideIcons.eyeOff
                        : LucideIcons.eye,
                    size: AppTheme.iconSm,
                  ),
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildTermsCheckbox(SignUpController controller) {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Checkbox(
              value: controller.acceptTerms.value,
              onChanged: (value) => controller.acceptTerms.value = value ?? false,
              activeColor: NoteColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusXs),
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: AppTheme.spacingMd),
                child: Text.rich(
                  TextSpan(
                    text: 'I agree to the ',
                    style: TextStyle(
                      color: NoteColors.textSecondary,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'inter_regular',
                    ),
                    children: [
                      TextSpan(
                        text: 'Terms of Service',
                        style: TextStyle(
                          color: NoteColors.primary,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      const TextSpan(text: ' and '),
                      TextSpan(
                        text: 'Privacy Policy',
                        style: TextStyle(
                          color: NoteColors.primary,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget _buildSignUpButton(SignUpController controller) {
    return Obx(() => SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller.isFormValid && !controller.isLoading.value
                ? controller.signUp
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: NoteColors.primary,
              foregroundColor: NoteColors.onPrimary,
              disabledBackgroundColor: NoteColors.interactiveDisabled,
              elevation: controller.isFormValid ? 2 : 0,
              shadowColor: NoteColors.primary.withValues(alpha: 0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              ),
              padding: EdgeInsets.symmetric(vertical: 18.sp),
            ),
            child: controller.isLoading.value
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        NoteColors.onPrimary,
                      ),
                    ),
                  )
                : Text(
                    'Create Account',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'inter_semibold',
                    ),
                  ),
          ),
        ));
  }

  Widget _buildLoginLink(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: TextStyle(
            color: NoteColors.textSecondary,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter_regular',
          ),
        ),
        TextButton(
          onPressed: () => Get.to(() => const LoginScreen()),
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingSm),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            'Sign In',
            style: TextStyle(
              color: NoteColors.primary,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              fontFamily: 'inter_semibold',
            ),
          ),
        ),
      ],
    );
  }
}

class SignUpController extends GetxController {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  
  final RxBool isNameValid = false.obs;
  final RxBool isEmailValid = false.obs;
  final RxBool isPasswordValid = false.obs;
  final RxBool isConfirmPasswordValid = false.obs;
  final RxBool acceptTerms = false.obs;
  final RxBool isLoading = false.obs;
  final RxBool obscurePassword = true.obs;
  final RxBool obscureConfirmPassword = true.obs;

  bool get isFormValid => 
      isNameValid.value && 
      isEmailValid.value && 
      isPasswordValid.value && 
      isConfirmPasswordValid.value && 
      acceptTerms.value;

  void validateName(String name) {
    isNameValid.value = name.trim().length >= 2;
  }

  void validateEmail(String email) {
    isEmailValid.value = GetUtils.isEmail(email);
  }

  void validatePassword(String password) {
    isPasswordValid.value = password.length >= 8;
    // Re-validate confirm password when password changes
    if (confirmPasswordController.text.isNotEmpty) {
      validateConfirmPassword(confirmPasswordController.text);
    }
  }

  void validateConfirmPassword(String confirmPassword) {
    isConfirmPasswordValid.value = 
        confirmPassword.isNotEmpty && 
        confirmPassword == passwordController.text;
  }

  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  void toggleConfirmPasswordVisibility() {
    obscureConfirmPassword.value = !obscureConfirmPassword.value;
  }

  Future<void> signUp() async {
    if (!isFormValid) return;

    isLoading.value = true;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Navigate to upload image screen
      Get.to(() => const UploadImage());
    } catch (e) {
      Get.snackbar(
        'Error',
        'Sign up failed. Please try again.',
        backgroundColor: NoteColors.errorLight,
        colorText: NoteColors.error,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
}
