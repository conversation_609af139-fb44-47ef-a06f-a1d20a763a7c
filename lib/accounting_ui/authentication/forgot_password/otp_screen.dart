import 'package:accounting_app/accounting_ui/authentication/forgot_password/reset_password.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import '../../../my_widget/my_pinput.dart';
class OtpScreen extends StatelessWidget {
  const OtpScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 20.sp,),
              SvgPicture.asset("assets/image/mic.svg",height: 52,width: 60,),
              SizedBox(height: 15.sp,),
              Container(
                height: 12,width: 320,
                decoration: BoxDecoration(
                  color: NoteColors.c6c6c6.withValues(alpha: 0.10),
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(20.sp),topRight: Radius.circular(20.sp),),
                ),
              ),
              Container(
                height: 620,width: 360,
                decoration: BoxDecoration(
                  color: NoteColors.f1f1,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(20.sp),topRight: Radius.circular(20.sp),),
                ),
                child: Column(
                  children: [
                    SizedBox(height: 15.sp,),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                            padding:  EdgeInsets.only(right: 25.sp),
                            child: IconButton(onPressed: () {
                              context.back();
                            },
                              icon: Icon(
                                Icons.arrow_back_outlined,color: NoteColors.blackbold,size: 22.sp,),)
                        ),
                        TextButton(onPressed: (){},
                          child: Text("OTP Verification",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: NoteColors.blackbold,
                              fontSize: 18.sp,fontWeight: FontWeight.w600,
                              fontFamily: "gen_semibold",
                            )),).marginOnly(right: 39.sp),
                      ],
                    ),
                    SizedBox(height: 20.sp,),
                    Center(
                      child: Text("We’ve sent an OTP code to your email, please enter that code below to verify.",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: NoteColors.blackbold,
                          fontSize: 15.sp,fontWeight: FontWeight.w400,
                          fontFamily: "poppins_regular",
                        ),).marginSymmetric(horizontal: 20.sp),
                    ),
                    Padding(
                      padding:  EdgeInsets.only(left: 8.sp,right: 8.sp),
                      child: MyPinput(),
                    ),
                    Spacer(),
                    MyButton(
                      onPressed: (){
                         context.openScreen(ResetPassword());
                      },
                      text: "Verify",fontFamily: "pro_bold",fontSize: 17.sp,
                      paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
                      textColor:  NoteColors.white,
                      buttonColor:  NoteColors.bluePrimery,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.sp),
                      ),
                    ).marginSymmetric(horizontal: 20.sp,vertical: 16.sp),
          
          
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
