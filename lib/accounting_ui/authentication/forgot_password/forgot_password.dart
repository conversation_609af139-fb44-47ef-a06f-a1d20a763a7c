import 'package:accounting_app/accounting_ui/authentication/forgot_password/varify_email.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import '../../../my_widget/my_email_textfield.dart';
class ForgotPassword extends StatelessWidget {
  const ForgotPassword({super.key});
  @override
  Widget build(BuildContext context) {
    final TextEditingController controller = TextEditingController();
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 20.sp,),
              SvgPicture.asset("assets/image/mic.svg",height: 52,width: 60,),
              SizedBox(height: 15.sp,),
               Container(
                 height: 12,width: 320,
               decoration: BoxDecoration(
                 color: NoteColors.c6c6c6.withValues(alpha: 0.10),
               borderRadius: BorderRadius.only(topLeft: Radius.circular(20.sp),topRight: Radius.circular(20.sp),),
               ),
               ),
               Container(
                 height: 620,width: 360,
               decoration: BoxDecoration(
                 color: NoteColors.f1f1,
                 borderRadius: BorderRadius.only(topLeft: Radius.circular(20.sp),topRight: Radius.circular(20.sp),),
               ),
                 child: Column(
                   children: [
                     SizedBox(height: 15.sp,),
                     Row(
                       mainAxisAlignment: MainAxisAlignment.center,
                       children: [
                         Padding(
                           padding:  EdgeInsets.only(right: 25.sp),
                           child: IconButton(
                             onPressed: (){
                             context.back();
                           },
                             icon: Icon(
                             Icons.arrow_back_outlined,color: NoteColors.blackbold,size: 22.sp,),),
                         ),
                         TextButton(
                           onPressed: (){},
                           child: Text("Forget Password",
                           textAlign: TextAlign.center,
                           style: TextStyle(
                             color: NoteColors.blackbold,
                             fontSize: 18.sp,fontWeight: FontWeight.w600,
                             fontFamily: "gen_semibold",
                           ),),).marginOnly(right: 39.sp),
                       ],
                     ),
                     Center(
                       child: Text("Enter your email to reset the password",
                         textAlign: TextAlign.center,
                         style: TextStyle(
                           color: NoteColors.blackbold,
                           fontSize: 15.sp,fontWeight: FontWeight.w400,
                           fontFamily: "poppins_regular",
                         ),).marginSymmetric(horizontal: 26.sp),
                     ),

                     Padding(
                       padding:  EdgeInsets.only(
                           left: 23.sp,right: 24.sp,top: 20.sp),
                       child: MyEmail(

                         fillColor: NoteColors.white,
                         hintText: "Email address",
                         controller: controller,
                         errorText: "Enter valid Email",
                       ),
                     ),
                     Spacer(),
                     MyButton(
                       onPressed:  (){
                         context.openScreen(VerifyEmail());
                       },
                       text: "SUBMIT",fontFamily: "pro_bold",fontSize: 17.sp,
                       paddingVertical: EdgeInsets.symmetric(vertical: 16.sp),
                       textColor:  NoteColors.white,
                       buttonColor:  NoteColors.bluePrimery,
                       shape: RoundedRectangleBorder(
                         borderRadius: BorderRadius.circular(15.sp),
                       ),
                     ).marginSymmetric(horizontal: 20.sp,vertical: 10.sp),
          
          
                   ],
                 ),
               ),
            ],
          ),
        ),
      ),
    );
  }
}
