import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'auth/onboarding_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    goToLogin();
  }
  Future goToLogin() async {
    await Future.delayed(Duration(seconds: 4), () {});
    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => OnboardingScreen()));
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
                  Center(
                  child: SvgPicture.asset("assets/image/splashimage.svg",height: 78,width: 111,)),
        ],
      ),
    );
  }
}
