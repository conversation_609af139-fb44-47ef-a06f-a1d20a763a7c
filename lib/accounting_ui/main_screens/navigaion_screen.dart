import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_data.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sizer/sizer.dart';
import 'calls_screen/calls_screen.dart';
import 'home_screen/main_home.dart';
import 'inbox_screen/inbox_item.dart';
class NavigaionScreen extends StatefulWidget {
  const NavigaionScreen({super.key});
  @override
  State<NavigaionScreen> createState() => _NavigaionScreenState();
}

class _NavigaionScreenState extends State<NavigaionScreen> {
  late int _currentIndex = 0;
  PageController pageController = PageController();
  @override
  void initState() {
    super.initState();
    pageController = PageController(initialPage: _currentIndex);
  }
  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final navigationItems = [
      NavigationDestination(
        icon: SvgPicture.asset( "assets/image/nhome.svg",height: 38,width: 38,  colorFilter: ColorFilter.mode(
          NoteColors.blackbold.withValues(alpha: 0.50),BlendMode.srcIn,),),
        selectedIcon: SvgPicture.asset(
          "assets/image/house.fill.svg",height: 28,width: 28,  colorFilter: ColorFilter.mode(
          NoteColors.white,BlendMode.srcIn,),),
        label: 'Home',
      ),
      NavigationDestination(
        icon: Stack(
          children: [
            SvgPicture.asset(
              "assets/image/messages.svg",height: 26,width: 26,
              colorFilter: ColorFilter.mode(
                NoteColors.blackbold.withValues(alpha: 0.50),BlendMode.srcIn,),),
            Positioned(
             left: 15.sp,
              child: Container(
                height: 16.sp,width: 16.sp,
                decoration: BoxDecoration(
                  color: NoteColors.red,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text("3",style: TextStyle(color: NoteColors.white,fontSize: 8),),
                ),
              ),
            )
          ],
        ),
        selectedIcon:  SvgPicture.asset(
          "assets/image/nmessage.svg",height: 23,width: 23,
          colorFilter: ColorFilter.mode(NoteColors.white,BlendMode.srcIn,),),
        label: "Inbox",
      ),
      NavigationDestination(
        icon: Stack(
          children: [
            SvgPicture.asset(
              "assets/image/nphone.svg",height: 22,width: 22,  colorFilter: ColorFilter.mode(
              NoteColors.blackbold.withValues(alpha: 0.50),BlendMode.srcIn,),),
            Positioned(
              left: 12.0.sp,
              child: Container(
                height: 15.sp,width: 15.sp,
                decoration: BoxDecoration(
                  color: NoteColors.red,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text("3",style: TextStyle(color: NoteColors.white,fontSize: 8),),
                ),
              ),
            )
          ],
        ),
        selectedIcon: SvgPicture.asset(
          "assets/image/phone.fill.svg",height: 25,width: 25,
          colorFilter: ColorFilter.mode(NoteColors.white,BlendMode.srcIn,),
        ),
        label: 'Calls',
      ),
      NavigationDestination(
        icon: Stack(
          children: [
            SvgPicture.asset(
              "assets/image/nprofile.svg",height: 25,width: 25,
              colorFilter: ColorFilter.mode(
                NoteColors.blackbold.withValues(alpha: 0.50),BlendMode.srcIn,),),
            Positioned(
              right: 1.sp,
              child: Container(
                height: 7,width: 7,
                decoration: BoxDecoration(
                  color: NoteColors.red,
                  shape: BoxShape.circle,
                ),
              ),
            )
          ],
        ),
        selectedIcon : SvgPicture.asset(
          "assets/image/account.svg",height: 27,width: 27,
          colorFilter: ColorFilter.mode(NoteColors.white,BlendMode.srcIn,),),
        label: 'Profile',
      ),
    ];
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: PageView(
        controller: pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        physics: BouncingScrollPhysics(),
        scrollDirection: Axis.horizontal,
        children: [
          MainHome(),
          InboxItem(),
          CallsScreen(),
          ProfilesData(),
        ],
      ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 0),
              blurRadius: 30,
              spreadRadius: 0,
              color: NoteColors.gry.withValues(alpha: .30),

            )]
          ),
          child: NavigationBar(
            elevation: 0,
            selectedIndex: _currentIndex,
            onDestinationSelected: (index) {
              pageController.animateToPage(index, duration: Duration(milliseconds: 500), curve: Curves.ease,);
              setState(() {
                _currentIndex = index;
              });
              pageController.jumpToPage(index);
            },
            destinations: navigationItems,
            backgroundColor: NoteColors.white,
            indicatorShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            indicatorColor: NoteColors.bluePrimery,
            animationDuration: Duration(milliseconds: 500),
          ),
        )
    );
  }
}
