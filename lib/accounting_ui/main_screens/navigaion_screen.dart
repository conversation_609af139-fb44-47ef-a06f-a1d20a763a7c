import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_data.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';

import 'calls_screen/calls_screen.dart';
import 'home_screen/main_home.dart';
import 'inbox_screen/inbox_item.dart';

class NavigaionScreen extends StatefulWidget {
  const NavigaionScreen({super.key});
  @override
  State<NavigaionScreen> createState() => _NavigaionScreenState();
}

class _NavigaionScreenState extends State<NavigaionScreen> {
  late int _currentIndex = 0;
  PageController pageController = PageController();
  @override
  Widget build(BuildContext context) {
    final navigationItems = [
      NavigationDestination(
        icon: const Icon(
          LucideIcons.home,
          size: AppTheme.iconSm,
        ),
        selectedIcon: const Icon(
          LucideIcons.home,
          size: AppTheme.iconSm,
        ),
        label: 'Home',
      ),
      NavigationDestination(
        icon: Stack(
          clipBehavior: Clip.none,
          children: [
            const Icon(
              LucideIcons.messageSquare,
              size: AppTheme.iconSm,
            ),
            Positioned(
              right: -2,
              top: -2,
              child: Container(
                height: 8,
                width: 8,
                decoration: const BoxDecoration(
                  color: NoteColors.error,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
        selectedIcon: const Icon(
          LucideIcons.messageSquare,
          size: AppTheme.iconSm,
        ),
        label: "Messages",
      ),
      NavigationDestination(
        icon: Stack(
          clipBehavior: Clip.none,
          children: [
            const Icon(
              LucideIcons.phone,
              size: AppTheme.iconSm,
            ),
            Positioned(
              right: -2,
              top: -2,
              child: Container(
                height: 8,
                width: 8,
                decoration: const BoxDecoration(
                  color: NoteColors.error,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
        selectedIcon: const Icon(
          LucideIcons.phone,
          size: AppTheme.iconSm,
        ),
        label: 'Calls',
      ),
      NavigationDestination(
        icon: Stack(
          clipBehavior: Clip.none,
          children: [
            const Icon(
              LucideIcons.user,
              size: AppTheme.iconSm,
            ),
            Positioned(
              right: -2,
              top: -2,
              child: Container(
                height: 6,
                width: 6,
                decoration: const BoxDecoration(
                  color: NoteColors.error,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
        selectedIcon: const Icon(
          LucideIcons.user,
          size: AppTheme.iconSm,
        ),
        label: 'Profile',
      ),
    ];
    return Scaffold(
        backgroundColor: NoteColors.white,
        body: PageView(
          controller: pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          physics: BouncingScrollPhysics(),
          scrollDirection: Axis.horizontal,
          children: [
            MainHome(),
            InboxItem(),
            CallsScreen(),
            ProfilesData(),
          ],
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: NoteColors.background,
            boxShadow: [
              BoxShadow(
                offset: const Offset(0, -2),
                blurRadius: 8,
                spreadRadius: 0,
                color: NoteColors.shadow,
              )
            ],
          ),
          child: NavigationBar(
            elevation: 0,
            selectedIndex: _currentIndex,
            onDestinationSelected: (index) {
              pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
              setState(() {
                _currentIndex = index;
              });
            },
            destinations: navigationItems,
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            indicatorShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
            ),
            indicatorColor: NoteColors.primary,
            animationDuration: const Duration(milliseconds: 300),
          ),
        ));
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    pageController = PageController(initialPage: _currentIndex);
  }
}
