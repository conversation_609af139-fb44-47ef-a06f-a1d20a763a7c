import 'package:accounting_app/accounting_ui/main_screens/home_screen/home_layout/home_layout.dart';
import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_list_tile.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

class MainHome extends StatelessWidget {
  MainHome({super.key});

  final RxList<Map<String, dynamic>> founds = List<Map<String, dynamic>>.from(
      homeData).obs;

  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      founds.assignAll(List.from(homeData));
    } else {
      founds.assignAll(
        homeData.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: NoteColors.bluePrimery,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: NoteColors.white,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));
    var image = "https://picsum.photos/200/200?random=$context";
    TextEditingController controller = TextEditingController();
    return Scaffold(
      backgroundColor: NoteColors.fafa,
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                height: 170, width: 380,
                decoration: BoxDecoration(
                  color: NoteColors.bluePrimery,
                ),
                child: MyListTile(
                  title: Text(
                    "Hi Handwerker! ", style: TextStyle(
                    color: NoteColors.white,
                    fontWeight: FontWeight.w400,
                    fontFamily: "gen_regular",
                    fontSize: 20.sp,
                  ),).marginOnly(top: 26.sp),
                  subtitle: Text(
                    "Find best", style: TextStyle(
                    color: NoteColors.white,
                    fontWeight: FontWeight.w600,
                    fontFamily: "gen_semibold",
                    fontSize: 23.sp,
                  ),),
                  trailing: Transform.translate(
                    offset: Offset(0, 24),
                    child: Container(
                      height: 80, width: 80,
                      decoration: BoxDecoration(
                        color: NoteColors.blue,
                        image: DecorationImage(
                          image: CachedNetworkImageProvider(image),
                        ), shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ),
              _buildTextField("Search...", controller, TextInputType.name,
                  [ FilteringTextInputFormatter.singleLineFormatter,]),
            ],
          ),
          Expanded(
            child: Obx(() {
              return ListView.builder(
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  scrollDirection: Axis.vertical,
                  itemCount: founds.length,
                  itemBuilder: (context, index) {
                    // var image = "https://picsum.photos/200/200?random=$index";
                    var item = founds[index];
                    return HomeLayout(item: item,);
                  });
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(String hint, TextEditingController controller,
      TextInputType keyboardType, List<TextInputFormatter>? inputFormatters) {
    return Padding(
      padding: EdgeInsets.only(left: 21.sp, right: 18.sp, top: 51.sp,),
      child: TextField(
        autofocus: true,
        onChanged: _runFilter,
        inputFormatters: inputFormatters,
        keyboardType: keyboardType,
        controller: controller,
        decoration: InputDecoration(
          filled: true,
          border: InputBorder.none,
          hintText: hint,
          hintStyle: TextStyle(
              color: NoteColors.blackbold.withValues(alpha: 0.50),
              fontFamily: "gen_regular",
              fontSize: 15.sp,
              fontWeight: FontWeight.w400),
          fillColor: NoteColors.white,
          contentPadding: EdgeInsets.symmetric(
              horizontal: 15.sp, vertical: 15.sp),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.sp),
            borderSide: BorderSide(
              color: NoteColors.white,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.sp),
            borderSide: BorderSide(
              color: NoteColors.white,
            ),
          ),
          suffixIcon: IconButton(
              onPressed: () => _removeLastCharacter(controller),
              icon: Icon(
                Icons.close, size: 18.sp, color: NoteColors.fieldborder,)),
          prefixIcon: Padding(
            padding: EdgeInsets.all(16.sp),
            child: SvgPicture.asset("assets/image/searching.svg", height: 10,
              width: 10,
              colorFilter: ColorFilter.mode(NoteColors.gry, BlendMode.srcIn),),
          ),
        ),
      ),
    );
  }

  void _removeLastCharacter(TextEditingController controller) {
    String currentText = controller.text;
    if (currentText.isNotEmpty) {
      controller.clear();
      controller.selection = TextSelection.fromPosition(
          TextPosition(offset: controller.text.length));
    }
  }
}
//  trailing: Padding(
//                                 padding:  EdgeInsets.only(left: 10.sp,top: 15.sp),
//                                 child: MyText(
//                                   title: item["date"],fontWeight: FontWeight.w400,fontSize: 15.sp,fontFamily: "sora_regular", color: NoteColors.blackbold,),
//                               ),

///myListtile
//MyListTile(
//                       tileColor: NoteColors.white,
//                       shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(12.sp)
//                       ),
//                       images: Container(
//                         height: 50,width: 50,
//                         decoration: BoxDecoration(
//                           color: NoteColors.blue,
//                           image: DecorationImage(
//                             image: CachedNetworkImageProvider(image),
//                           ), shape: BoxShape.circle,
//                         ),
//                       ),
//                       title: Text(
//                         item["name"]!,style: TextStyle(
//                         color: NoteColors.blackbold,fontWeight: FontWeight.w500,fontFamily: "poppins_regular",
//                         fontSize: 17.sp,
//                       ),),
//                       subtitle: Column(
//                         children: [
//                           Text(
//                             item["email"]!,style: TextStyle(
//                             color: NoteColors.blackbold,fontWeight: FontWeight.w500,fontFamily: "poppins_regular",
//                             fontSize: 14.sp,
//                           ),),
//
//                           Row(mainAxisAlignment: MainAxisAlignment.start,
//                             children: [
//                               Text(
//                                 item["time"]!,style: TextStyle(
//                                 color: NoteColors.blackbold,fontWeight: FontWeight.w400,fontFamily: "poppins_regular",
//                                 fontSize: 13.sp,
//                               ),),
//
//                             ],
//                           ),
//                         ],
//                       ),
//                       //tr
//                     ),
///bildlistview
//Padding(
//                     padding: EdgeInsets.only(left: 15.sp,right: 15.sp,top: 12.sp),
//                     child: Container(
//                       height: 180,width: 350,
//                       decoration: BoxDecoration(
//                         boxShadow: [
//                           BoxShadow(
//                             color: NoteColors.backgrond,
//                             offset: Offset(0, 0),
//                             spreadRadius: 0,
//                             blurRadius: 10,
//                           )
//                         ],
//                         color: NoteColors.white,
//                         borderRadius: BorderRadius.circular(10.sp),
//                       ),
//                       child: Column(
//                         children: [
//                           Row(
//                             children: [
//                               Padding(
//                                 padding:  EdgeInsets.only(left: 18.sp,top: 16.sp),
//                                 child: Container(
//                                   height: 90,width: 96,
//                                   decoration: BoxDecoration(
//                                     borderRadius: BorderRadius.circular(10.sp),
//                                     color: NoteColors.white,
//                                     image: DecorationImage(
//                                       image: CachedNetworkImageProvider(image),
//                                     ),
//                                     shape: BoxShape.rectangle,
//                                   ),
//                                 ),
//                               ),
//                               Column(
//                                 mainAxisAlignment: MainAxisAlignment.start,
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     item["name"]!,
//                                     style: TextStyle(
//                                     color: NoteColors.blackbold,fontWeight: FontWeight.w500,fontFamily: "gen_medium",
//                                     fontSize: 18.sp,
//                                   ),
//                                   ),
//                                   Text(
//                                     "Professional Person",
//                                     style: TextStyle(
//                                       color: NoteColors.halfblues,fontWeight: FontWeight.w500,fontFamily: "cookie_regular",
//                                       fontSize: 18.sp,
//                                     ),
//                                   ),
//                                   Text(
//                                     item["exp"]!,
//                                       style: TextStyle(
//                                       color: NoteColors.blackbold.withValues(alpha: .70),fontWeight: FontWeight.w500,fontFamily: "gen_regular",
//                                       fontSize: 15.sp,
//                                     ),
//                                   ),
//                                   Row(
//                                     children: [
//                                       Container(
//                                         height: 11,width: 11,
//                                         decoration: BoxDecoration(
//                                           color: NoteColors.bluePrimery,
//                                            shape: BoxShape.circle
//                                         ),
//                                       ),
//                                       Text(
//                                         item["client"]!,
//                                         style: TextStyle(
//                                           color: NoteColors.blackbold,fontWeight: FontWeight.w500,fontFamily: "pro_regular",
//                                           fontSize: 15.sp,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ],
//                               ).marginOnly(left: 10.sp,top: 13.sp),
//                             ],
//                           ),
//                           MyListTile(
//                             title: Text(
//                               "Available",
//                               style: TextStyle(
//                               color: NoteColors.halfblues,fontWeight: FontWeight.w600,fontFamily: "pro_regular",
//                               fontSize: 15.sp,
//                             ),),
//                             subtitle: Text(
//                               "24-HRS",
//                               style: TextStyle(
//                                 color: NoteColors.blackbold,fontWeight: FontWeight.w600,fontFamily: "pro_semibold",
//                                 fontSize: 15.sp,
//                               ),),
//                             trailing: Container(
//                               height: 35,width: 140,
//                               decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(10.sp),
//                                 color: NoteColors.bluePrimery,
//                               ),
//                               child: Center(
//                                 child: Text(
//                                   "Get Consultation",
//                                   style: TextStyle(
//                                     color: NoteColors.white,fontWeight: FontWeight.w600,fontFamily: "pro_bold",
//                                     fontSize: 15.sp,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   );

///
