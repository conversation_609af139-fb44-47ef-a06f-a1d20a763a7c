import 'package:accounting_app/accounting_ui/main_screens/home_screen/home_layout/home_layout.dart';
import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:sizer/sizer.dart';

class MainHome extends StatelessWidget {
  MainHome({super.key});

  final RxList<Map<String, dynamic>> founds = List<Map<String, dynamic>>.from(
      homeData).obs;

  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      founds.assignAll(List.from(homeData));
    } else {
      founds.assignAll(
        homeData.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: NoteColors.background,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));

    TextEditingController controller = TextEditingController();
    
    return Scaffold(
      backgroundColor: NoteColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Professional Header
            _buildHeader(context),

            // Main Content
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    // Quick Stats Cards
                    _buildQuickStats(),

                    SizedBox(height: AppTheme.spacingLg),

                    // Search Section
                    _buildSearchSection(controller),

                    SizedBox(height: AppTheme.spacingLg),

                    // Recent Transactions
                    _buildRecentTransactions(),

                    SizedBox(height: AppTheme.spacingXl),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: NoteColors.background,
        boxShadow: [
          BoxShadow(
            color: NoteColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Avatar
          Container(
            width: 48.sp,
            height: 48.sp,
            decoration: BoxDecoration(
              color: NoteColors.primaryContainer,
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              border: Border.all(
                color: NoteColors.primary.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              child: CachedNetworkImage(
                imageUrl: "https://picsum.photos/200/200?random=1",
                fit: BoxFit.cover,
                placeholder: (context, url) => Icon(
                  LucideIcons.user,
                  color: NoteColors.primary,
                  size: AppTheme.iconMd,
                ),
                errorWidget: (context, url, error) => Icon(
                  LucideIcons.user,
                  color: NoteColors.primary,
                  size: AppTheme.iconMd,
                ),
              ),
            ),
          ),

          SizedBox(width: AppTheme.spacingMd),

          // Welcome Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back,',
                  style: TextStyle(
                    color: NoteColors.textSecondary,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'inter_regular',
                  ),
                ),
                Text(
                  'John Doe',
                  style: TextStyle(
                    color: NoteColors.textPrimary,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'inter_semibold',
                  ),
                ),
              ],
            ),
          ),

          // Notification Button
          Container(
            decoration: BoxDecoration(
              color: NoteColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              border: Border.all(
                color: NoteColors.borderLight,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () {},
              icon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(
                    LucideIcons.bell,
                    size: AppTheme.iconSm,
                  ),
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: NoteColors.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
              style: IconButton.styleFrom(
                foregroundColor: NoteColors.textPrimary,
                padding: EdgeInsets.all(AppTheme.spacingMd),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final stats = [
      {
        'title': 'Total Balance',
        'value': '\$12,450.00',
        'icon': LucideIcons.wallet,
        'color': NoteColors.primary,
        'trend': '+12.5%',
        'isPositive': true,
      },
      {
        'title': 'Monthly Income',
        'value': '\$8,200.00',
        'icon': LucideIcons.trendingUp,
        'color': NoteColors.success,
        'trend': '+8.2%',
        'isPositive': true,
      },
      {
        'title': 'Monthly Expenses',
        'value': '\$3,750.00',
        'icon': LucideIcons.trendingDown,
        'color': NoteColors.warning,
        'trend': '-5.1%',
        'isPositive': false,
      },
    ];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        children: [
          // First card (larger)
          _buildStatCard(stats[0], isLarge: true),
          
          SizedBox(height: AppTheme.spacingMd),
          
          // Two smaller cards
          Row(
            children: [
              Expanded(child: _buildStatCard(stats[1])),
              SizedBox(width: AppTheme.spacingMd),
              Expanded(child: _buildStatCard(stats[2])),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(Map<String, dynamic> stat, {bool isLarge = false}) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: NoteColors.surface,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        border: Border.all(
          color: NoteColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: NoteColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppTheme.spacingSm),
                decoration: BoxDecoration(
                  color: (stat['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                ),
                child: Icon(
                  stat['icon'] as IconData,
                  color: stat['color'] as Color,
                  size: isLarge ? AppTheme.iconMd : AppTheme.iconSm,
                ),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingSm,
                  vertical: AppTheme.spacingXs,
                ),
                decoration: BoxDecoration(
                  color: stat['isPositive']
                      ? NoteColors.successLight
                      : NoteColors.errorLight,
                  borderRadius: BorderRadius.circular(AppTheme.radiusXs),
                ),
                child: Text(
                  stat['trend'] as String,
                  style: TextStyle(
                    color: stat['isPositive']
                        ? NoteColors.success
                        : NoteColors.error,
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'inter_semibold',
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppTheme.spacingMd),
          
          Text(
            stat['title'] as String,
            style: TextStyle(
              color: NoteColors.textSecondary,
              fontSize: isLarge ? 14.sp : 12.sp,
              fontWeight: FontWeight.w400,
              fontFamily: 'inter_regular',
            ),
          ),
          
          SizedBox(height: AppTheme.spacingXs),
          
          Text(
            stat['value'] as String,
            style: TextStyle(
              color: NoteColors.textPrimary,
              fontSize: isLarge ? 24.sp : 18.sp,
              fontWeight: FontWeight.w700,
              fontFamily: 'inter_bold',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection(TextEditingController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Transactions',
            style: TextStyle(
              color: NoteColors.textPrimary,
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              fontFamily: 'inter_semibold',
            ),
          ),
          
          SizedBox(height: AppTheme.spacingMd),
          
          TextFormField(
            controller: controller,
            onChanged: _runFilter,
            decoration: InputDecoration(
              hintText: 'Search by name, amount, or category...',
              prefixIcon: const Icon(
                LucideIcons.search,
                size: AppTheme.iconSm,
              ),
              suffixIcon: IconButton(
                onPressed: () {},
                icon: const Icon(
                  LucideIcons.sliders,
                  size: AppTheme.iconSm,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: TextStyle(
                  color: NoteColors.textPrimary,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'inter_semibold',
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.to(() => const HomeLayout());
                },
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: NoteColors.primary,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'inter_semibold',
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppTheme.spacingMd),
          
          Obx(() => ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: founds.length > 5 ? 5 : founds.length,
                separatorBuilder: (context, index) => 
                    SizedBox(height: AppTheme.spacingSm),
                itemBuilder: (context, index) {
                  final item = founds[index];
                  return _buildTransactionItem(item);
                },
              )),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> item) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: NoteColors.surface,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        border: Border.all(
          color: NoteColors.borderLight,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 40.sp,
            height: 40.sp,
            decoration: BoxDecoration(
              color: NoteColors.primaryContainer,
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              child: CachedNetworkImage(
                imageUrl: item["image"] ?? "",
                fit: BoxFit.cover,
                placeholder: (context, url) => Icon(
                  LucideIcons.user,
                  color: NoteColors.primary,
                  size: AppTheme.iconSm,
                ),
                errorWidget: (context, url, error) => Icon(
                  LucideIcons.user,
                  color: NoteColors.primary,
                  size: AppTheme.iconSm,
                ),
              ),
            ),
          ),

          SizedBox(width: AppTheme.spacingMd),

          // Transaction Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item["name"] ?? "Unknown",
                  style: TextStyle(
                    color: NoteColors.textPrimary,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'inter_semibold',
                  ),
                ),
                Text(
                  item["subtitle"] ?? "Transaction",
                  style: TextStyle(
                    color: NoteColors.textSecondary,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'inter_regular',
                  ),
                ),
              ],
            ),
          ),

          // Amount
          Text(
            item["trailing"] ?? "\$0.00",
            style: TextStyle(
              color: NoteColors.textPrimary,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              fontFamily: 'inter_semibold',
            ),
          ),
        ],
      ),
    );
  }
}
