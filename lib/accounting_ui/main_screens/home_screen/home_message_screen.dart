import 'package:accounting_app/accounting_ui/main_screens/calls_screen/video_call_screen.dart';
import 'package:accounting_app/accounting_ui/main_screens/calls_screen/voice_call_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sizer/sizer.dart';
class HomeMessageScreen extends StatelessWidget {
  HomeMessageScreen({super.key, required this.image, required this.name});
  final String image;
  final String name;
  final Rx<int> length = 0.obs;
  final RxList<Map<String, dynamic>> messages = RxList([]);
  final TextEditingController textController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    final EmojiController controller = Get.put(EmojiController());
    return Scaffold(
      backgroundColor: NoteColors.ff8f8f8,
      body: Column(
        children: [
          Container(
            height: 120,
            width: 380,
            decoration: BoxDecoration(
              color: NoteColors.white,
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: Icon(
                    Icons.arrow_back_ios, size: 30,).marginOnly(left: 20.sp),
                ).marginOnly(top: 20.sp),
                Container(
                  height: 55, width: 55,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                        image: CachedNetworkImageProvider(image)),
                  ),
                ).marginOnly(top: 20.sp),
                Column(
                  children: [
                    Text(
                      name, style: TextStyle(color: NoteColors.blackbold,
                      fontWeight: FontWeight.w700,
                      fontFamily: "poppins_semibold",
                      fontSize: 18.sp,),).marginOnly(top: 30.sp),
                    Text(
                      "   Support Member", style: TextStyle(
                      color: NoteColors.blugry,
                      fontWeight: FontWeight.w400,
                      fontFamily: "poppins_regular",
                      fontSize: 15.sp,),),
                  ],
                ),
                Row(
                  children: [
                    GestureDetector(
                        onTap: () {
                          context.openScreen(VoiceCallScreen(name: name, image: image,));
                        },
                        child: SvgPicture.asset(
                          "assets/image/phone.svg", height: 24,
                          width: 24,
                          colorFilter: ColorFilter.mode(
                              NoteColors.blackbold.withValues(alpha: .50),
                              BlendMode.srcIn),)),
                    SizedBox(width: 12.sp),
                    GestureDetector(
                        onTap: () {
                          context.openScreen(VideoCallScreen(title: name, image: image,));
                        },
                        child: SvgPicture.asset(
                          "assets/image/videocall.svg", height: 28,
                          width: 28,
                          colorFilter: ColorFilter.mode(
                              NoteColors.blackbold.withValues(alpha: .50),
                              BlendMode.srcIn),)),
                  ],
                ).marginOnly(left: 25.sp, top: 22.sp),
              ],
            ),
          ),
          Expanded(
            child: Obx(() {
              return ListView.builder(
                  itemCount: messages.length,
                  shrinkWrap: true,
                  itemBuilder: (_, index) {
                    var data = messages[index];
                    var send = index % 2 == 0;
                     return  Align(
                     alignment: send ? Alignment.topRight : Alignment.topLeft,
                     child: Column(
                       children: [
                         Card(
                           color: NoteColors.ff8f8f8,
                           elevation: 0,
                           child: Padding(
                             padding: send ? EdgeInsets.only(left: 39.sp) : EdgeInsets.only(right: 20.sp) ,
                             child: IntrinsicWidth(
                               child: Padding(
                                 padding:  EdgeInsets.only(left: 12.sp,right: 12.sp),
                                 child: Container(
                                   decoration: send ?
                                   BoxDecoration(
                                     borderRadius: BorderRadius.only(bottomRight: Radius.circular(20),topRight: Radius.circular(0),topLeft: Radius.circular(20),bottomLeft: Radius.circular(20)),
                                     color: NoteColors.bluePrimery,
                                   ) :
                                   BoxDecoration(
                                       borderRadius: BorderRadius.only(bottomRight: Radius.circular(20),topRight: Radius.circular(20),topLeft: Radius.circular(20),bottomLeft: Radius.circular(0)),
                                       color: NoteColors.white,
                                   ),
                                 child: ListTile(
                                     tileColor: NoteColors.ff8f8f8,
                                     title: Text(data["text"].toString(),style: TextStyle(fontSize: 16.sp,fontFamily: "poppins_medium",fontWeight: FontWeight.w500,color: send ? NoteColors.white : NoteColors.blackbold),),
                                     subtitle: Text(
                                       data["timestamp"], style: TextStyle(color:send ? NoteColors.white : NoteColors.blackbold.withValues(alpha: .60),fontWeight: FontWeight.w500,fontFamily: "poppins_regular",fontSize: 14.sp),).marginOnly(top: 10.sp),
                                   ),
                                 ),
                               ),
                             ),
                           ),
                         )
                       ],
                     ),
                   );
                  });
            }),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: TextFormField(
                  focusNode: controller.focusNode,
                  controller: controller.textController,
                  onChanged: (data) {
                    length.value = data.length;
                  },
                  decoration: InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(15.sp),
                        child: GestureDetector(
                          onTap: (){
                            controller.isEmoji.value = !controller.isEmoji.value;
                            controller.focusNode.unfocus();
                            controller.focusNode.canRequestFocus = true;
                          },
                          child: SvgPicture.asset(
                            "assets/image/emoticon (1).svg", height: 20,
                            width: 20,),
                        ),
                      ),
                      suffixIcon: GestureDetector(
                        onTap: () {
                          var text = controller.textController.text;
                          _sendMessage(text);
                        },
                        child: Container(
                          height: 45, width: 45,
                          decoration: BoxDecoration(
                            color: NoteColors.halfblues.withValues(
                                alpha: .12),
                            shape: BoxShape.circle,
                          ), child: Center(
                          child: SvgPicture.asset(
                            "assets/image/sening.svg", height: 20,
                            width: 20,),
                        ),).marginOnly(right: 12.sp, top: 5.sp, bottom: 5.sp),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: NoteColors.white,)
                      ),
                      focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: NoteColors.white),
                      ),
                      hintText: "Type message here...",
                      hintStyle: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w400,
                        fontFamily: "poppins_regular",
                        color: NoteColors.gryText,
                      ),
                      contentPadding: EdgeInsets.symmetric( vertical: 20.sp),
                  ),
                  onFieldSubmitted: (data) {
                    _sendMessage(data);
                  },
                  style: TextStyle(
                      color: NoteColors.blackbold,fontSize: 16.sp,fontFamily: "poppins_medium",fontWeight: FontWeight.w500),
                  maxLines: 2,
                  minLines: 1,
                ),
              ),
            ],
          ),
          Obx(() {
            return Offstage(
              offstage: !controller.isEmoji.value,
              child: SizedBox(
                height: 250,
                child: EmojiPicker(
                  onEmojiSelected: (category, emoji) {
                    controller.textController.text = controller.textController.text + emoji.emoji;
                  },
                  onBackspacePressed: () {
                    print('Backspace pressed');
                  },
                  config: Config(
                    height: 256,
                    checkPlatformCompatibility: true,
                    emojiViewConfig: EmojiViewConfig(
                      emojiSizeMax: 32,
                    ),
                    viewOrderConfig: const ViewOrderConfig(
                      top: EmojiPickerItem.categoryBar,
                      middle: EmojiPickerItem.emojiView,
                      bottom: EmojiPickerItem.searchBar,
                    ),
                    skinToneConfig: const SkinToneConfig(),
                    categoryViewConfig: const CategoryViewConfig(),
                    bottomActionBarConfig: const BottomActionBarConfig(),
                    searchViewConfig: const SearchViewConfig(),
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
  void _sendMessage(String text) {
    final EmojiController controller = Get.put(EmojiController());
    if (text.isNotEmpty) {
      String currentTime = DateFormat('hh:mm a').format(DateTime.now());
      messages.add({
        "text": text,
        "timestamp": currentTime,
      });
      controller.textController.clear();
      length.value = 0;
    }
  }
}
//SizedBox(width: 10.sp,),
//                 Container(
//                   height: 50,width: 50,
//                   decoration: BoxDecoration(
//                       color: NoteColors.blackbold,
//                       borderRadius: BorderRadius.circular(10.sp)
//                   ),
//                   child: GestureDetector(
//                       onTap: () {
//                         var text = textController.text;
//                         _sendMessage(text);
//                       },
//                       child: Padding(
//                         padding: EdgeInsets.all(12),
//                         child: Icon(Icons.send, color: NoteColors.white,),
//                       )),
//                 ),
///
