import 'dart:math';
import 'package:accounting_app/accounting_ui/main_screens/home_screen/home_message_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_list_tile.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
class HomeLayout extends StatelessWidget {
  const HomeLayout({super.key,required this.item});
 final   Map<String, dynamic> item;
  @override
  Widget build(BuildContext context) {
    final randomImage = randomImages[Random().nextInt(randomImages.length)];
    // var image = item["image"] ?? "https://picsum.photos/200/200?random=$context";
    return Padding(
      padding: EdgeInsets.only(left: 15.sp,right: 15.sp,top: 12.sp),
      child: Container(
        height: 180,width: 350,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: NoteColors.backgrond,
              offset: Offset(0, 0),
              spreadRadius: 0,
              blurRadius: 10,
            )
          ],
          color: NoteColors.white,
          borderRadius: BorderRadius.circular(10.sp),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Padding(
                  padding:  EdgeInsets.only(left: 18.sp,top: 16.sp),
                  child: Container(
                    height: 90,width: 96,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.sp),
                      color: NoteColors.white,
                      image: DecorationImage(
                        image: CachedNetworkImageProvider(randomImage),
                      ),
                      shape: BoxShape.rectangle,
                    ),
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item["name"]!,
                      style: TextStyle(
                        color: NoteColors.blackbold,fontWeight: FontWeight.w500,fontFamily: "gen_medium",
                        fontSize: 18.sp,
                      ),
                    ),
                    Text(
                      "Professional Person",
                      style: TextStyle(
                        color: NoteColors.halfblues,fontWeight: FontWeight.w500,fontFamily: "cookie_regular",
                        fontSize: 19.sp,
                      ),
                    ),
                    Text(
                      item["exp"]!,
                      style: TextStyle(
                        color: NoteColors.blackbold.withValues(alpha: .70),fontWeight: FontWeight.w500,fontFamily: "gen_regular",
                        fontSize: 15.sp,
                      ),
                    ),
                    Row(
                      children: [
                        Container(
                          height: 11,width: 11,
                          decoration: BoxDecoration(
                              color: NoteColors.bluePrimery,
                              shape: BoxShape.circle
                          ),
                        ),
                        Text(
                          item["client"]!,
                          style: TextStyle(
                            color: NoteColors.blackbold,fontWeight: FontWeight.w500,fontFamily: "pro_regular",
                            fontSize: 15.sp,
                          ),
                        ),
                      ],
                    ),
                  ],
                ).marginOnly(left: 10.sp,top: 13.sp),
              ],
            ),
            MyListTile(
              title: Text(
                "Available",
                style: TextStyle(
                  color: NoteColors.halfblues,fontWeight: FontWeight.w600,fontFamily: "pro_regular",
                  fontSize: 15.sp,
                ),),
              subtitle: Text(
                "24-HRS",
                style: TextStyle(
                  color: NoteColors.blackbold,fontWeight: FontWeight.w600,fontFamily: "pro_semibold",
                  fontSize: 15.sp,
                ),),
              trailing: GestureDetector(
                onTap: () {
                  context.openScreen(HomeMessageScreen(image: randomImage, name: item["name"]!,));
                },
                child: Container(
                  height: 38,width: 145,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.sp),
                    color: NoteColors.bluePrimery,
                  ),
                  child: Center(
                    child: Text(
                      "Get Consultation",
                      style: TextStyle(
                        color: NoteColors.white,fontWeight: FontWeight.w600,fontFamily: "pro_bold",
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                ),
              )
            ),
          ],
        ),
      ),
    );
  }
}
