import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import 'done_screen.dart';
class VoiceCallScreen extends StatelessWidget {
  const VoiceCallScreen({super.key, required this.name,required this.image});
  final String name;
  final String image;
  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    // var image = "https://picsum.photos/200/200?random=$context";
    final RxBool isMuted = false.obs;
    final RxBool isSpeakerOn = false.obs;
    return Scaffold(
      backgroundColor: NoteColors.white,
       body: Safe<PERSON>rea(
        child: Column(
          children: [
            SizedBox(height: 40.sp,),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  "assets/image/dialcall.svg", height: 16, width: 16,
                ),
                SizedBox(width: 12.sp,),
                Text(
                    "Calling......", style: TextStyle(
                  color: NoteColors.gry,
                  fontWeight: FontWeight.w600,
                  fontFamily: "inter_regular",
                  fontSize: 15.sp,))
              ],
            ),
            SizedBox(height: 50.sp,),
            Container(
              height: 70, width: 70,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                    image: CachedNetworkImageProvider(image)),
              ),
            ),
            SizedBox(height: 10.sp,),
            Text(
                name, style: TextStyle(
              color: NoteColors.blackbold,
              fontWeight: FontWeight.w600,
              fontFamily: "pro_meidum",
              fontSize: 20.sp,)),
            Text(
                "+91 **********", style: TextStyle(
              color: NoteColors.gry,
              fontWeight: FontWeight.w500,
              fontFamily: "inter_regular",
              fontSize: 14.sp,)),
            SizedBox(height: 20.sp,),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Obx(() {
                  return GestureDetector(
                    onTap: () => isMuted.value = !isMuted.value,
                    child: Container(
                      height: 56, width: 56,
                      decoration: BoxDecoration(
                          color: isMuted.value ? NoteColors.gry : NoteColors
                              .f9fa,
                          shape: BoxShape.circle,
                          border: Border.all(color: NoteColors.e5e5)
                      ),
                      child: Center(child: SvgPicture.asset(
                        "assets/image/mic-off.svg", height: 26, width: 26,)),
                    ),
                  );
                }),
                Obx(() {
                  return GestureDetector(
                    onTap:() =>  isSpeakerOn.value = !isSpeakerOn.value,
                    child: Container(
                      height: 56, width: 56,
                      decoration: BoxDecoration(
                          color:isSpeakerOn.value ? NoteColors.gry : NoteColors.f9fa,
                          shape: BoxShape.circle,
                          border: Border.all(color: NoteColors.e5e5)
                      ),
                      child: Center(
                          child: SvgPicture.asset(
                        "assets/image/volume-2.svg", height: 26, width: 26,)),
                    ),
                  );
                }),
              ],
            ),
            SizedBox(height: 10.sp,),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text("  Mute", style: TextStyle(color: NoteColors.hihggry,
                  fontWeight: FontWeight.w400,
                  fontFamily: "inter_medium",
                  fontSize: 16.sp,

                ),),
                Text("  Speaker", style: TextStyle(color: NoteColors.hihggry,
                  fontWeight: FontWeight.w400,
                  fontFamily: "inter_medium",
                  fontSize: 16.sp,

                ),),
              ],
            ),
            SizedBox(height: 40.sp,),
            GestureDetector(
                onTap: () {
                 context.openScreen(DoneScreen());
                },
                child: SvgPicture.asset(
                  "assets/image/cancelcall.svg", height: 64, width: 64,))
          ],
        ),
      ),
    );
  }
}
