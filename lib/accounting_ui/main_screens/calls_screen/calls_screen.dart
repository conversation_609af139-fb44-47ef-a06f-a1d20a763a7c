import 'package:accounting_app/accounting_ui/main_screens/calls_screen/video_call_screen.dart';
import 'package:accounting_app/accounting_ui/main_screens/calls_screen/voice_call_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:sizer/sizer.dart';
import '../../../item_list/item_lists.dart';

class CallsScreen extends StatelessWidget {
  CallsScreen({super.key});
  
  final RxList<Map<String, dynamic>> fondUser = List<Map<String, dynamic>>.from(
      callHistory).obs;
  final RxString selectedFilter = 'All'.obs;

  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      fondUser.assignAll(List.from(callHistory));
    } else {
      fondUser.assignAll(
        callHistory.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    final TextEditingController controller = TextEditingController();
    
    return Scaffold(
      backgroundColor: NoteColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Professional Header
            _buildHeader(),

            // Filter Tabs
            _buildFilterTabs(),

            // Search Section
            _buildSearchSection(controller),

            // Calls List
            Expanded(
              child: _buildCallsList(),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: NoteColors.background,
        boxShadow: [
          BoxShadow(
            color: NoteColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Call History',
                  style: TextStyle(
                    color: NoteColors.textPrimary,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'inter_bold',
                  ),
                ),
                Text(
                  'Manage your recent calls',
                  style: TextStyle(
                    color: NoteColors.textSecondary,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'inter_regular',
                  ),
                ),
              ],
            ),
          ),
          
          // Notification Button
          Container(
            decoration: BoxDecoration(
              color: NoteColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              border: Border.all(
                color: NoteColors.borderLight,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () {},
              icon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(
                    LucideIcons.bell,
                    size: AppTheme.iconSm,
                  ),
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: NoteColors.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
              style: IconButton.styleFrom(
                foregroundColor: NoteColors.textPrimary,
                padding: EdgeInsets.all(AppTheme.spacingMd),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    final filters = ['All', 'Missed', 'Incoming', 'Outgoing'];
    
    return Container(
      height: 50,
      margin: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        separatorBuilder: (context, index) => SizedBox(width: AppTheme.spacingSm),
        itemBuilder: (context, index) {
          final filter = filters[index];
          return Obx(() => FilterChip(
                label: Text(filter),
                selected: selectedFilter.value == filter,
                onSelected: (selected) {
                  if (selected) {
                    selectedFilter.value = filter;
                  }
                },
                backgroundColor: NoteColors.surface,
                selectedColor: NoteColors.primaryContainer,
                checkmarkColor: NoteColors.primary,
                labelStyle: TextStyle(
                  color: selectedFilter.value == filter
                      ? NoteColors.primary
                      : NoteColors.textSecondary,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'inter_medium',
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                  side: BorderSide(
                    color: selectedFilter.value == filter
                        ? NoteColors.primary
                        : NoteColors.borderLight,
                    width: 1,
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMd,
                  vertical: AppTheme.spacingSm,
                ),
              ));
        },
      ),
    );
  }

  Widget _buildSearchSection(TextEditingController controller) {
    return Padding(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      child: TextFormField(
        controller: controller,
        onChanged: _runFilter,
        decoration: InputDecoration(
          hintText: 'Search contacts, numbers...',
          prefixIcon: const Icon(
            LucideIcons.search,
            size: AppTheme.iconSm,
          ),
          suffixIcon: IconButton(
            onPressed: () {},
            icon: const Icon(
              LucideIcons.sliders,
              size: AppTheme.iconSm,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCallsList() {
    return Obx(() => ListView.separated(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
          itemCount: fondUser.length,
          separatorBuilder: (context, index) => 
              SizedBox(height: AppTheme.spacingSm),
          itemBuilder: (context, index) {
            final call = fondUser[index];
            return _buildCallItem(call);
          },
        ));
  }

  Widget _buildCallItem(Map<String, dynamic> call) {
    final callType = call["callType"] ?? "incoming";
    final isMissed = callType == "missed";
    
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: NoteColors.surface,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        border: Border.all(
          color: NoteColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: NoteColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Avatar
          Container(
            width: 48.sp,
            height: 48.sp,
            decoration: BoxDecoration(
              color: NoteColors.primaryContainer,
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              child: CachedNetworkImage(
                imageUrl: call["image"] ?? "",
                fit: BoxFit.cover,
                placeholder: (context, url) => Icon(
                  LucideIcons.user,
                  color: NoteColors.primary,
                  size: AppTheme.iconMd,
                ),
                errorWidget: (context, url, error) => Icon(
                  LucideIcons.user,
                  color: NoteColors.primary,
                  size: AppTheme.iconMd,
                ),
              ),
            ),
          ),

          SizedBox(width: AppTheme.spacingMd),

          // Call Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        call["name"] ?? "Unknown",
                        style: TextStyle(
                          color: isMissed ? NoteColors.error : NoteColors.textPrimary,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'inter_semibold',
                        ),
                      ),
                    ),
                    _buildCallTypeIcon(callType),
                  ],
                ),
                
                SizedBox(height: AppTheme.spacingXs),
                
                Row(
                  children: [
                    Text(
                      call["subtitle"] ?? "Mobile",
                      style: TextStyle(
                        color: NoteColors.textSecondary,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'inter_regular',
                      ),
                    ),
                    Text(
                      ' • ',
                      style: TextStyle(
                        color: NoteColors.textTertiary,
                        fontSize: 12.sp,
                      ),
                    ),
                    Text(
                      call["time"] ?? "Just now",
                      style: TextStyle(
                        color: NoteColors.textSecondary,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'inter_regular',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Action Buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildActionButton(
                icon: LucideIcons.phone,
                onPressed: () => Get.to(() => const VoiceCallScreen()),
                color: NoteColors.success,
              ),
              SizedBox(width: AppTheme.spacingSm),
              _buildActionButton(
                icon: LucideIcons.video,
                onPressed: () => Get.to(() => const VideoCallScreen()),
                color: NoteColors.primary,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCallTypeIcon(String callType) {
    IconData icon;
    Color color;
    
    switch (callType) {
      case 'missed':
        icon = LucideIcons.phoneIncoming;
        color = NoteColors.error;
        break;
      case 'incoming':
        icon = LucideIcons.phoneIncoming;
        color = NoteColors.success;
        break;
      case 'outgoing':
        icon = LucideIcons.phoneOutgoing;
        color = NoteColors.primary;
        break;
      default:
        icon = LucideIcons.phone;
        color = NoteColors.textSecondary;
    }
    
    return Icon(
      icon,
      size: AppTheme.iconXs,
      color: color,
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: AppTheme.iconSm,
          color: color,
        ),
        style: IconButton.styleFrom(
          padding: EdgeInsets.all(AppTheme.spacingSm),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {},
      backgroundColor: NoteColors.primary,
      foregroundColor: NoteColors.onPrimary,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      ),
      child: const Icon(
        LucideIcons.phone,
        size: AppTheme.iconMd,
      ),
    );
  }
}
