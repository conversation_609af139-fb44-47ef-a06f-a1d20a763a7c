import 'package:accounting_app/accounting_ui/main_screens/calls_screen/video_call_screen.dart';
import 'package:accounting_app/accounting_ui/main_screens/calls_screen/voice_call_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import '../../../item_list/item_lists.dart';
class CallsScreen extends StatelessWidget {
  CallsScreen({super.key,required });
  final RxList<Map<String, dynamic>> fondUser = List<Map<String, dynamic>>.from(
      callHistory).obs;
  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      fondUser.assignAll(List.from(callHistory));
    } else {
      fondUser.assignAll(
        callHistory.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }
  @override
  Widget build(BuildContext context) {
    final TextEditingController controller = TextEditingController();
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 14.sp),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Calls history", style: TextStyle(
                    color: NoteColors.blackbold,
                    fontWeight: FontWeight.w400,
                    fontFamily: "gen_semibold",
                    fontSize: 24.sp,),),
                  SvgPicture.asset(
                    "assets/image/notify.svg", height: 42, width: 42,),
                ],
              ).marginSymmetric(horizontal: 18.sp),
              _buildTextField("Search...", controller, TextInputType.name,
                  [ FilteringTextInputFormatter.singleLineFormatter,]),
              SizedBox(height: 20.sp,),
              Obx(() {
                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                            "Today", style: TextStyle(
                          color: NoteColors.blackbold,
                          fontWeight: FontWeight.w600,
                          fontFamily: "pro_semibold",
                          fontSize: 18.sp,)).marginOnly(left: 18.sp, top: 14
                            .sp),
                      ],
                    ),
                    ListView.builder(
                        shrinkWrap: true,
                        itemCount: fondUser.length,
                        physics: BouncingScrollPhysics(),
                        itemBuilder: (BuildContext context, index) {
                          var calls = fondUser[index];
                          return Column(
                            children: [
                              MyListTile(
                                images: SvgPicture.asset(
                                  "assets/image/misscall.svg", height: 16,
                                  width: 16,),
                                title: Text(
                                    calls["name"]!, style: TextStyle(
                                  color: NoteColors.blackbold,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: "pro_semibold",
                                  fontSize: 17.sp,)),
                                subtitle: Text(
                                    maxLines: 1,
                                    calls["subName"]!, style: TextStyle(
                                  color: NoteColors.gryhigh,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: "poppins_regular",
                                  fontSize: 14.sp,)),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      calls["time"]!, style: TextStyle(
                                      color: NoteColors.blackbold,
                                      fontSize: 15.sp,
                                      fontFamily: "inter_regular",
                                      fontWeight: FontWeight.w400,
                                    ),),
                                    SizedBox(width: 18.sp,),
                                    GestureDetector(
                                      onTap: () {
                                        context.openScreen(VoiceCallScreen(
                                          name: calls["name"]!, image: "",));
                                      },
                                      child: SvgPicture.asset(
                                        "assets/image/phone.svg", height: 24,
                                        width: 24,
                                        colorFilter: ColorFilter.mode(
                                            NoteColors.blackbold.withValues(
                                                alpha: .50),
                                            BlendMode.srcIn),),
                                    ),
                                    SizedBox(width: 12.sp),
                                    GestureDetector(
                                      onTap: () {
                                        context.openScreen(VideoCallScreen(title: calls["name"]!, image: "",));
                                      },
                                      child: SvgPicture.asset(
                                        "assets/image/videocall.svg",
                                        height: 28,
                                        width: 28,
                                        colorFilter: ColorFilter.mode(
                                            NoteColors.blackbold.withValues(
                                                alpha: .50),
                                            BlendMode.srcIn),),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 15.sp, right: 15.sp),
                                child: Divider(
                                  color: NoteColors.bord.withValues(alpha: .50),
                                ),
                              ),
                            ],
                          );
                        }),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                            "Yesterday", style: TextStyle(
                          color: NoteColors.blackbold,
                          fontWeight: FontWeight.w600,
                          fontFamily: "pro_semibold",
                          fontSize: 18.sp,)).marginOnly(left: 18.sp, top: 14
                            .sp),
                      ],
                    ),
                    ListView.builder(
                        shrinkWrap: true,
                        itemCount: fondUser.length,
                        physics: BouncingScrollPhysics(),
                        itemBuilder: (BuildContext context, index) {
                          var calls = fondUser[index];
                          return Column(
                            children: [
                              MyListTile(
                                images: SvgPicture.asset(
                                  "assets/image/misscall.svg", height: 16,
                                  width: 16,),
                                title: Text(
                                    calls["name"]!, style: TextStyle(
                                  color: NoteColors.blackbold,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: "pro_semibold",
                                  fontSize: 17.sp,)),
                                subtitle: Text(
                                    maxLines: 1,
                                    calls["subName"]!, style: TextStyle(
                                  color: NoteColors.gryhigh,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: "poppins_regular",
                                  fontSize: 14.sp,)),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      calls["time"]!, style: TextStyle(
                                      color: NoteColors.blackbold,
                                      fontSize: 15.sp,
                                      fontFamily: "inter_regular",
                                      fontWeight: FontWeight.w400,

                                    ),),
                                    SizedBox(width: 18.sp,),
                                    GestureDetector(
                                      onTap: () {
                                        context.openScreen(VoiceCallScreen(
                                          name: calls["name"]!, image: '',));
                                      },
                                      child: SvgPicture.asset(
                                        "assets/image/phone.svg", height: 24,
                                        width: 24,
                                        colorFilter: ColorFilter.mode(
                                            NoteColors.blackbold.withValues(
                                                alpha: .50),
                                            BlendMode.srcIn),),
                                    ),
                                    SizedBox(width: 12.sp,),

                                    GestureDetector(
                                      onTap: () {
                                        context.openScreen(VideoCallScreen(title: calls["name"]!, image: '',));
                                      },
                                      child: SvgPicture.asset(
                                        "assets/image/videocall.svg",
                                        height: 28,
                                        width: 28,
                                        colorFilter: ColorFilter.mode(
                                            NoteColors.blackbold.withValues(
                                                alpha: .50),
                                            BlendMode.srcIn),),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 15.sp, right: 15.sp),
                                child: Divider(
                                  color: NoteColors.bord,
                                ),
                              ),
                            ],
                          );
                        }),
                  ],
                );
              }),
            ],
          ),
        ),
      ),
    );
  }
  Widget _buildTextField(String hint, TextEditingController controller,
      TextInputType keyboardType, List<TextInputFormatter>? inputFormatters) {
    return Padding(
      padding: EdgeInsets.only(left: 21.sp, right: 18.sp, top: 10.sp,),
      child: TextField(
        onChanged: _runFilter,
        autofocus: true,
        inputFormatters: inputFormatters,
        keyboardType: keyboardType,
        controller: controller,
        decoration: InputDecoration(
          filled: true,
          border: UnderlineInputBorder(
            borderSide: BorderSide(
              color: NoteColors.gry.withValues(alpha: .30),
              width: 1.5.sp,
            ),
          ),
          hintText: hint,
          hintStyle: TextStyle(
              color: NoteColors.blackbold.withValues(alpha: 0.50),
              fontFamily: "gen_regular",
              fontSize: 15.sp,
              fontWeight: FontWeight.w400),
          fillColor: NoteColors.white,
          contentPadding: EdgeInsets.symmetric(
              horizontal: 15.sp, vertical: 15.sp),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(
              color: NoteColors.gry,
              width: 1.5.sp,
            ),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(
              color: NoteColors.gry,
              width: 2.sp,
            ),
          ),
          prefixIcon: Padding(
            padding: EdgeInsets.all(16.sp),
            child: SvgPicture.asset("assets/image/searching.svg", height: 10,
              width: 10,
              colorFilter: ColorFilter.mode(NoteColors.gry, BlendMode.srcIn),),
          ),
        ),
      ),
    );
  }
}
