import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import 'calls_screen.dart';
class DoneScreen extends StatelessWidget {
  const DoneScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                height: 550, width: 380,
                decoration: BoxDecoration(
                  color: NoteColors.white,
                ),
                child: Image.asset(
                  "assets/image/done.png", fit: BoxFit.cover,
                  filterQuality: FilterQuality.high,),
              ),
              Center(
                child: Text(
                 "Thank You!", style: TextStyle(
                  color: NoteColors.blackbold,
                  fontWeight: FontWeight.w600,
                  fontFamily: "gen_semibold",
                  fontSize: 22.sp,)),
              ).marginOnly(top: 86.sp),
              Center(
                child: Text(
                    "We really appreciate you using (App name). ",
                    style: TextStyle(
                  color: NoteColors.gryhigh,
                  fontWeight: FontWeight.w400,
                  fontFamily: "pro_medium",
                  fontSize: 16.sp,)),
              ).marginOnly(top: 90.sp),
            ],
          ),
          SizedBox(height: 50.sp,),
          GestureDetector(
            onTap: (){
              context.openScreen(CallsScreen());
            },
            child: Container(
              height: 49,width: 330,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5.sp),
                color: NoteColors.bluePrimery,
              ),
              child: Center(
                child: Text(
                    "Back to Home",
                    style: TextStyle(
                  color: NoteColors.white,
                  fontWeight: FontWeight.w700,
                  fontFamily: "poppins_regular",
                  fontSize: 16.sp,)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
