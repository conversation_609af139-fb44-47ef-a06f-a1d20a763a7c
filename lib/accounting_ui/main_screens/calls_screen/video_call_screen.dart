
import 'package:accounting_app/accounting_ui/main_screens/calls_screen/done_screen.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
class VideoCallScreen extends StatefulWidget {
  const VideoCallScreen({super.key, required this.title,required this.image});
  final String title;
  final String image;
  @override
  State<VideoCallScreen> createState() => _VideoCallScreenState();
}
class _VideoCallScreenState extends State<VideoCallScreen> {
  late List<CameraDescription> _cameras;
  late CameraController controllers;
  bool _isCameraInitialized = false;
  bool _isFrontCamera = false;
  @override
  void initState() {
    super.initState();
    _initializeCamera(_isFrontCamera);
  }
  Future<void> _initializeCamera(bool isFront) async {
    try {
      _cameras = await availableCameras();
      if (_cameras.isNotEmpty) {
        int cameraIndex = isFront && _cameras.length > 1 ? 1 : 0;
        if (_isCameraInitialized) {
          await controllers.dispose();
        }
        controllers = CameraController(
            _cameras[cameraIndex], ResolutionPreset.max, enableAudio: false);
        await controllers.initialize();
        await controllers.lockCaptureOrientation(
            isFront ? DeviceOrientation.landscapeLeft : DeviceOrientation.landscapeRight);
        if (mounted) {
          setState(() {
            _isCameraInitialized = true;
          });
        }
      }
    } catch (e) {
      // print("Camera initialization error: $e");
    }
  }
  void _toggleCamera() async {
    setState(() {
      _isFrontCamera = !_isFrontCamera;
    });
    await _initializeCamera(_isFrontCamera);
  }
  @override
  void dispose() {
    controllers.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    final RxBool isMuted = false.obs;
    final RxBool isSpeakerOn = false.obs;
    // var image = "https://picsum.photos/360/720?random=$context";
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: Column(
          children: [
            _isCameraInitialized ?
            Obx(() {
              return Stack(
                children: [
                  Center(
                      child:


                      SizedBox(
                        height: 740, width: 380,
                        child: AspectRatio(
                          aspectRatio: controllers.value.aspectRatio,
                          child: isSpeakerOn.value
                              ? Container(
                            height: 740,width: 380,
                            decoration: BoxDecoration(
                              color: NoteColors.blackbold.withValues(alpha: .90),
                            ),
                            child: Center(
                              child: Container(
                                height: 80, width: 80,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  image: DecorationImage(
                                      image: CachedNetworkImageProvider(widget.image),fit: BoxFit.cover),

                                ),
                              ),
                            ),
                          )
                              : Transform.scale(
                              scaleX: _isFrontCamera ? -1 : 1,
                              child: CameraPreview(controllers,))),
                      )),
                    Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        height: 160, width: 120,
                        decoration: BoxDecoration(
                          color: NoteColors.white,
                          borderRadius: BorderRadius.circular(20.sp),
                        ), child: CameraPreview(controllers,),).marginOnly(
                          top: 30.sp),
                      GestureDetector(
                        onTap: _toggleCamera,
                        child: SvgPicture.asset(
                          "assets/image/Flip Camera.svg", height: 55,
                          width: 55,)

                            .marginOnly(bottom: 25.sp),
                      ),
                    ],
                  ).marginSymmetric(horizontal: 22.sp, vertical: 20.sp),
                  Center(
                    child: Obx(() =>
                        Text(
                          controller.currentTime.value,
                          style: TextStyle(
                              color: NoteColors.white,
                              fontSize: 18.sp,
                              fontFamily: "poppins_medium",
                              fontWeight: FontWeight.w700
                          ),
                        )),
                  ).marginOnly(top: 90.sp),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                        GestureDetector(
                          onTap: () => isMuted.value = !isMuted.value,
                          child: Container(
                            height: 56, width: 56,
                            decoration: BoxDecoration(
                              color: isMuted.value
                                  ? NoteColors.bluePrimery
                                  : NoteColors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: isMuted.value ? SvgPicture.asset(
                                "assets/image/mic_on.svg", height: 26,
                                width: 26,
                                colorFilter: ColorFilter.mode(
                                    NoteColors.white, BlendMode.srcIn),
                              ) : SvgPicture.asset(
                                "assets/image/mic_off.svg", height: 26,
                                width: 26,
                                colorFilter: ColorFilter.mode(
                                    NoteColors.blackbold, BlendMode.srcIn),
                              ),),
                          ),
                        ),
                       GestureDetector(
                          onTap: () => isSpeakerOn.value = !isSpeakerOn.value,
                          child: Container(
                            height: 56, width: 56,
                            decoration: BoxDecoration(
                              color: isSpeakerOn.value
                                  ? NoteColors.white
                                  : NoteColors.bluePrimery,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                                child: isSpeakerOn.value ? SvgPicture.asset(
                                  "assets/image/video-camera-off-svgrepo-com.svg",
                                  height: 24, width: 24,
                                  colorFilter: ColorFilter.mode(
                                      NoteColors.blackbold, BlendMode.srcIn),
                                ) : SvgPicture.asset(
                                  "assets/image/video-camera-svgrepo-com (1).svg",
                                  height: 26, width: 24,
                                  colorFilter: ColorFilter.mode(
                                      NoteColors.white, BlendMode.srcIn),)),
                          ),
                        ),
                    ],
                  ).marginOnly(top: 96.sp),
                  Center(
                    child: GestureDetector(
                      onTap: () {
                        context.openScreen(DoneScreen());
                      },
                      child: SvgPicture.asset(
                        "assets/image/cancelcall.svg", height: 65, width: 65,)
                          .marginOnly(
                          top: 107.sp, right: 4
                      ),
                    ),
                  ),
                  Center(
                    child: Text(widget.title, style: TextStyle(color: NoteColors
                        .white,
                        fontWeight: FontWeight.w600,
                        fontFamily: "poppins_semibold",
                        fontSize: 18.sp),),).marginOnly(top: 15.sp),
                  Center(
                    child: Text("Ringing....",
                      style: TextStyle(color: NoteColors.white,
                          fontWeight: FontWeight.w400,
                          fontFamily: "poppins_regular",
                          fontSize: 16.sp),),).marginOnly(top: 25.sp),
                  SizedBox(height: 15.sp,),
                ],
              );
            })
                : Center(child: SizedBox(),),
          ],
        ),
      ),
    );
  }
}


///data
// Container(
//                   height: 740, width: 380,
//                   decoration: BoxDecoration(
//                     color: NoteColors.white,
//                   ),
//                   child: Image.asset(
//                     "assets/image/image 2.png", fit: BoxFit.cover,
//                     filterQuality: FilterQuality.high,),
//                 ),
//                 //
//                 SizedBox(height: 20.sp,),
//                 //
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Image.asset("assets/image/image 4.png", fit: BoxFit.cover,
//                       height: 200,
//                       width: 140,).marginOnly(top: 20.sp),
//                     GestureDetector(
//
//                       child: SvgPicture.asset(
//                         "assets/image/Flip Camera.svg", height: 55, width: 55,)
//                           .marginOnly(bottom: 25.sp),
//                     ),
//                   ],
//                 ).marginSymmetric(horizontal: 22.sp),
//                 //
//                 Center(
//                   child: Obx(() => Text(
//                     controller.currentTime.value,
//                     style: TextStyle(
//                         color: NoteColors.white,
//                         fontSize: 18.sp,
//                         fontFamily: "poppins_medium",
//                         fontWeight: FontWeight.w700
//                     ),
//                   )),
//                 ).marginOnly(top: 90.sp),
//                 //
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceAround,
//                   children: [
//                     Obx(() {
//                       return GestureDetector(
//                         onTap: () => isMuted.value = !isMuted.value,
//                         child: Container(
//                           height: 56, width: 56,
//                           decoration: BoxDecoration(
//                               color: isMuted.value ? NoteColors.bluePrimery : NoteColors.white,
//                               shape: BoxShape.circle,
//                               border: Border.all(color: NoteColors.e5e5)
//                           ),
//                           child: Center(
//                               child: isMuted.value ? SvgPicture.asset(
//                             "assets/image/mic_on.svg", height: 26, width: 26,
//                                 colorFilter: ColorFilter.mode(
//                                     NoteColors.white, BlendMode.srcIn),
//                               ) : SvgPicture.asset(
//                                 "assets/image/mic_off.svg", height: 26, width: 26,
//                                 colorFilter: ColorFilter.mode(
//                                     NoteColors.blackbold, BlendMode.srcIn),
//                               ), ),
//
//                         ),
//                       );
//                     }),
//                     Obx(() {
//                       return GestureDetector(
//                         onTap:() =>  isSpeakerOn.value = !isSpeakerOn.value,
//                         child: Container(
//                           height: 56, width: 56,
//                           decoration: BoxDecoration(
//                               color:isSpeakerOn.value ? NoteColors.bluePrimery : NoteColors.white,
//                               shape: BoxShape.circle,
//
//                           ),
//                           child: Center(
//                               child: isSpeakerOn.value ? SvgPicture.asset(
//                                 "assets/image/video-camera-svgrepo-com (1).svg", height: 24, width: 24,
//                                 colorFilter: ColorFilter.mode(
//                                     NoteColors.white, BlendMode.srcIn),
//                               ) : SvgPicture.asset(
//                                 "assets/image/video-camera-off-svgrepo-com.svg", height: 26, width: 24,
//                                 colorFilter: ColorFilter.mode(
//                                     NoteColors.blackbold, BlendMode.srcIn),)  ),
//                         ),
//                       );
//                     }),
//                   ],
//                 ).marginOnly(top: 96.sp),
//                 //
//                 Center(
//                   child: GestureDetector(
//                     onTap: (){
//                       context.openScreen(DoneScreen());
//                     },
//                     child: SvgPicture.asset(
//                       "assets/image/cancelcall.svg", height: 65, width: 65,)
//                         .marginOnly(
//                         top: 107.sp, right: 4
//                     ),
//                   ),
//                 ),