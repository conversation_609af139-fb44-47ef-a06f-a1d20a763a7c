import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:accounting_app/my_widget/my_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
class MyDetail extends StatelessWidget {
  const MyDetail({super.key});
  @override
  Widget build(BuildContext context) {
    final MyController controller = Get.put(MyController());
    final TextEditingController nameController = TextEditingController();
    final TextEditingController emailController = TextEditingController();
    final DatePickerController dateController =  Get.put(DatePickerController());
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: NoteColors.white,
        surfaceTintColor: NoteColors.white,
        title:  Text(
          "My Details", style: TextStyle(
          color: NoteColors.blackbold,
          fontWeight: FontWeight.w600,
          fontFamily: "gen_semibold",
          fontSize: 21.sp,),),
      ),
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildText("Full Name"),
              _buildTextField("Enter your name",nameController,TextInputType.name,[ FilteringTextInputFormatter.singleLineFormatter,]),
              _buildText("Email Address"),
              _buildTextField("Enter your Email",emailController,TextInputType.emailAddress,[ FilteringTextInputFormatter.singleLineFormatter,]),
              _buildText("Date of Birth"),
              Padding(
                padding: EdgeInsets.only(left: 18.sp, right: 18.sp, top: 10.sp,bottom: 15.sp),
                child: Obx(() {
                  return TextField(
                    controller: dateController.dateController.value,
                    onTap: () {
                      dateController.selectDate(context);
                    },
                    readOnly: true,
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 15.sp, vertical: 10),
                      hintText: "${dateController.selectedDate.value.day},"
                          " ${dateController.selectedDate.value.month} "
                          "${dateController.selectedDate.value.year}",
                      hintStyle: TextStyle(
                          color: NoteColors.blackbold.withValues(alpha: .50),
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w400,
                          fontFamily: "poppins_regular"
                      ),
                      suffixIcon: Padding(
                        padding:  EdgeInsets.all(16.sp),
                        child: SvgPicture.asset(
                            "assets/image/birth.svg", height: 18, width: 20,),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),  // Rounded Border
                        borderSide: BorderSide(color: NoteColors.e5e5, width: 1), // Change Color
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),  // Rounded Border
                        borderSide: BorderSide(color: NoteColors.e5e5, width: 1), // Change Color
                      ),
                    ),
                  );
                }),
              ),
              _buildText("Gender"),
              Obx(()=>
                  MyDropDown(
                    selectedValue: controller.gender.value,
                    items: controller.genderList,
                    borderRadius: BorderRadius.circular(13.sp),
                    onChanged: (String? value) {
                      if(value != null) {
                        controller.selectGender(value);
                      }
                    },),
              ),
              SizedBox(height: 65.sp,),
              GestureDetector(
                onTap: (){
                  // context.openScreen(CallsScreen());
                },
                child: Container(
                  height: 49,width: 330,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5.sp),
                    color: NoteColors.bluePrimery,
                  ),
                  child: Center(
                    child: Text(
                        "Update changes!",
                        style: TextStyle(
                          color: NoteColors.white,
                          fontWeight: FontWeight.w700,
                          fontFamily: "poppins_regular",
                          fontSize: 16.sp,)),
                  ),
                ),
              ).marginSymmetric(vertical: 15.sp),
            ],
          ),
        ),
      ),
    );
  }
  Widget _buildTextField(String hint,TextEditingController controller,TextInputType keyboardType,List<TextInputFormatter>? inputFormatters){
    return Padding(
      padding: EdgeInsets.only(left: 20.sp,right: 18.sp,top: 10.sp,bottom: 15.sp),
      child: TextField(
        autofocus: true,
        inputFormatters: inputFormatters,
        keyboardType: keyboardType,
        controller: controller,
        decoration: InputDecoration(
          filled: true,
          border: InputBorder.none,
          hintText: hint,
          hintStyle: TextStyle(
              color: NoteColors.blackbold.withValues(alpha: 0.50),
              fontFamily: "poppins_regular",
              fontSize: 14.sp,
              fontWeight: FontWeight.w400),
          fillColor: NoteColors.white,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.sp,vertical: 15.sp),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.sp),
            borderSide: BorderSide(
              color:  NoteColors.e5e5,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.sp),
            borderSide: BorderSide(
              color:  NoteColors.e5e5,
            ),
          ),
          // suffixIcon:    IconButton(
          //     onPressed:  () => _removeLastCharacter(controller),
          //     icon: Icon(Icons.close,size: 18.sp,color: NoteColors.fieldborder,)),
        ),
      ),
    );
  }
  Widget _buildText(String title){
    return Padding(
      padding:  EdgeInsets.only(left: 20.sp,top: 8.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(title,
            style: TextStyle(
            color: NoteColors.blackbold,
            fontWeight: FontWeight.w600,fontFamily: "pro_medium",
            fontSize: 18.sp,
          ),),
        ],
      ),
    );
  }
}
