import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

class FaqScreen extends StatelessWidget {
  FaqScreen({super.key});
  final MyController controller = Get.put(MyController());
  final RxList<Map<String, dynamic>> fondUser = List<Map<String, dynamic>>.from(faqQuestion).obs;
  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      fondUser.assignAll(List.from(faqQuestion));
    } else {
      fondUser.assignAll(
        faqQuestion.where((question) => question["faqsQuestion"]!.toLowerCase().contains(enterKeyboard.toLowerCase())).toList(),
      );
    }
  }
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        surfaceTintColor: NoteColors.white,
        backgroundColor: NoteColors.white,
        centerTitle: true,
        title: Text(
          "FAQs",
          style: TextStyle(
            color: NoteColors.blackbold,
            fontFamily: "gen_semibold",
            fontWeight: FontWeight.w600,
            fontSize: 20.sp,
          ),
        ),
      ),
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 6.sp),
              child: Divider(color: NoteColors.e6e6e6),
            ),
            SizedBox(height: 20.sp),
            Padding(
              padding: EdgeInsets.only(left: 8),
              child: SizedBox(
                height: 40,
                child: ListView.builder(
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: faqList.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () => controller.current.value = index,
                      child: Obx(
                            () => Container(
                          margin: EdgeInsets.only(left: 16.sp),
                          height: 36,
                          width: 97,
                          decoration: BoxDecoration(
                            color: controller.current.value == index ? NoteColors.bluePrimery : NoteColors.white,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: controller.current.value == index ? NoteColors.bluePrimery : NoteColors.e6e6e6,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              faqList[index],
                              style: TextStyle(
                                color: controller.current.value == index ? NoteColors.white : NoteColors.blackbold,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                fontFamily: "gen_medium",
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 15.sp),
            Padding(
              padding: EdgeInsets.only(top: 16.sp),
              child: SizedBox(
                height: screenHeight * .070,
                width: screenWidth * .88,
                child: TextField(
                  onChanged: _runFilter,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: NoteColors.white,
                    prefixIcon: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12),
                      child: SvgPicture.asset("assets/image/Searchfaq.svg", height: 24, width: 24),
                    ),
                    hintText: 'Search for questions...',
                    hintStyle: TextStyle(
                      fontFamily: "pro_regular",
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: NoteColors.gry,
                    ),
                    suffixIcon: Padding(
                      padding: EdgeInsets.all(13.0),
                      child: SvgPicture.asset("assets/image/micfaq.svg", height: 24, width: 24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(14.sp),
                      borderSide: BorderSide(color: NoteColors.e6e6e6),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(14.sp),
                      borderSide: BorderSide(color: NoteColors.e6e6e6),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Obx(
                    ()=> ListView.builder(
                  physics: BouncingScrollPhysics(),
                  itemCount: fondUser.length,
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    final question = fondUser[index];
                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 14.sp),
                      child: Theme(
                        data: ThemeData(dividerColor: Colors.transparent),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: NoteColors.e6e6e6),
                            borderRadius: BorderRadius.circular(10.sp),
                          ),
                          child: ExpansionTile(
                            title: Text(
                              question["faqsQuestion"]!,
                              style: TextStyle(
                                fontFamily: "pro_semibold",
                                color: NoteColors.blackbold,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 17.sp, vertical: 15.sp),
                                child: Text(
                                  question["faqAnswer"]!,
                                  style: TextStyle(
                                    fontFamily: "pro_regular",
                                    color: NoteColors.gryText,
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
///
//   initState () {
//      fondUser =  List.from(faqQuestion);
//       super.initState();
//    }
//    void _runFilter(String enterKeyboard){
//      List<Map<String , dynamic>> result = [];
//        if (enterKeyboard.isEmpty) {
//          result = List.from(faqQuestion);
//        }  else {
//          result = faqQuestion
//              .where((question) =>
//              question["faqsQuestion"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
//              .toList();
//        }
//        setState(() {
//          fondUser = result;
//        });
//    }