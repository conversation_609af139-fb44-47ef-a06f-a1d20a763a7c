import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
class ProfileDialog extends StatelessWidget {
  const ProfileDialog({super.key});
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    return Dialog(
      child: Container(
        height: screenHeight * .40, width: screenWidth * .70,
        decoration: BoxDecoration(
          color: NoteColors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 18.sp,),
            SvgPicture.asset("assets/image/error_dialog.svg",height: 60,width: 60,),
            SizedBox(height: 10.sp,),
            TextButton(
              onPressed: (){},
              child: Text("Logout?",
                style: TextStyle(
                color: NoteColors.blackbold,fontFamily: "gen_semibold",
                fontWeight: FontWeight.w600,fontSize: 20.sp,
              ),),),
            Padding(
              padding:  EdgeInsets.only(left: 15.sp,right: 10.sp),
              child: Text(
                "Are you sure you want to logout?",
                style: TextStyle(
                color: NoteColors.gryText,fontFamily: "pro_regular",
                fontWeight: FontWeight.w400,fontSize: 16.sp,
                ),),
            ),
            SizedBox(height: 15.sp),
            Container(
              height: 54,width: 250,
              decoration: BoxDecoration(
                color: NoteColors.reds,
                borderRadius: BorderRadius.circular(14.sp),
              ),
              child: Center(
                child: Text(
                "Yes, Logout",
                style: TextStyle(
                  color: NoteColors.white,fontFamily: "pro_medium",
                  fontWeight: FontWeight.w600,fontSize: 16.sp,
                ),),),
            ),
            SizedBox(height: 16.sp),

            GestureDetector(
              onTap: (){Get.back();},
              child: Container(
                height: 54,width: 250,
                decoration: BoxDecoration(
                  color: NoteColors.white,
                  border: Border.all(color: NoteColors.cccc),
                  borderRadius: BorderRadius.circular(14.sp),
                ),
                child: Center(
                  child: Text(
                    "No, Cancel",
                    style: TextStyle(
                      color: NoteColors.blackbold,fontFamily: "pro_medium",
                      fontWeight: FontWeight.w600,fontSize: 16.sp,
                    ),),),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
