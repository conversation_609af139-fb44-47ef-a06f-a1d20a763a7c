import 'package:accounting_app/my_color/note_color.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';
import 'ordertabs/active_screen.dart';
import 'ordertabs/all_order_screen.dart';
import 'ordertabs/cancel_screen.dart';
import 'ordertabs/complete_screen.dart';
class MyOrder extends StatelessWidget {
  const MyOrder({super.key});
  @override
  Widget build(BuildContext context) {
    Map <String, Widget> screenList = {
      "Orders" : AllOrderScreen(),
      "Complet" : CompleteScreen(),
      "Active" : ActiveScreen(),
      "Cancel" : CancelScreen(),
    };
    var image = "https://picsum.photos/200/200?random=$context";
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: DefaultTabController(
        length: screenList.length,
        animationDuration: Duration(milliseconds: 300),
        child: Expanded(
          child: Column(
            children: [
              Container(
                height: 120,
                width: 380,
                decoration: BoxDecoration(
                  color: NoteColors.bluePrimery,
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        Get.back();
                      },
                      icon: Icon(
                        Icons.arrow_back_outlined,size: 22.sp,color: NoteColors.white,).marginOnly(left: 12.sp),
                    ).marginOnly(top: 20.sp),
                    Stack(
                      children: [
                        Container(
                          height: 40, width: 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image: CachedNetworkImageProvider(image)),
                          ),
                        ).marginOnly(top: 20.sp),
                        Positioned(
                                top: 28.sp,left: 19.sp,
                          child: SvgPicture.asset("assets/image/fram.svg",height: 20,width: 20,)
                        ),
                      ],
                    ),
                    Text(
                      maxLines: 1,
                      "Orders",
                      style: TextStyle(color: NoteColors.white,
                        fontWeight: FontWeight.w500,
                        fontFamily: "inter_medium",
                        fontSize: 18.sp,),).marginOnly(top: 20.sp,left: 15.sp),
                      SvgPicture.asset("assets/image/Searchfaq.svg",
                      height: 24,
                      width: 24,
                      colorFilter: ColorFilter.mode(NoteColors.white, BlendMode.srcIn),).marginOnly(left: 50.sp, top: 22.sp),
                  ],
                ),
              ),
              SizedBox(height: 20.sp,),
              Builder(
                  builder: (BuildContext context) {
                    final TabController tabController = DefaultTabController.of(context);
                    return TabBar(
                      controller: tabController,
                      dividerHeight: 1,
                      indicatorSize: TabBarIndicatorSize.label,
                      indicatorColor: NoteColors.bluePrimery,
                      dividerColor: NoteColors.gryhigh.withValues(alpha: .20),
                      indicatorWeight: 5.0,
                      indicatorAnimation: TabIndicatorAnimation.elastic,
                      labelColor: NoteColors.bluePrimery,
                      unselectedLabelColor: NoteColors.blackbold.withValues(
                          alpha: 0.50),
                      unselectedLabelStyle: TextStyle(color: NoteColors.gry),
                      tabs: screenList.keys.map((service) =>
                          Tab(
                            child: Expanded(
                              child: SingleChildScrollView(
                                child: Column(
                                  children: [
                                    SizedBox(height: 13.sp,),
                                    Text(service,),
                                  ],
                                ),
                              ),
                            ),)).toList(),);
                  }
              ),
              Expanded(
                  child: TabBarView(children: screenList.values.toList())),


            ],
          ),
        ),
      ),
    );
  }
}
///
//AnimatedBuilder(
//                                       animation: tabController,
//                                       builder: (BuildContext context, Widget? child) {
//                                         final isSelected =
//                                             tabController.index == screenList.keys.toList().indexOf(service);
//                                         return Container(
//                                           height: 14, width: 14,
//                                           decoration: BoxDecoration(
//                                             color: isSelected ? NoteColors.bluePrimery : NoteColors.blackbold.withValues(alpha: .50),
//                                             shape: BoxShape.circle,
//                                           ),
//                                         );
//                                       },
//                                     ),