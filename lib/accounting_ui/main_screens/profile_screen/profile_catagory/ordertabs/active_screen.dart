import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sizer/sizer.dart';

class ActiveScreen extends StatelessWidget {
  const ActiveScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: Column(
        children: [
          SizedBox(height: 20.sp,),
          _buildOrder("Order#************", "\$30", "Active", "Title:", "category:", "Started Date:", "End Date:",32.sp,NoteColors.green),
        ],
      ),
    );
  }
  Widget _buildOrder(String orderId,price,situation,title,category,start,end,double? situationWidth,Color situationColor){
    return   Column(
      children: [
        Container(
          height: 188,width: 390,
          decoration: BoxDecoration(
            color: NoteColors.white,
          ),
          child: Column(
            children: [
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        orderId, style: TextStyle(
                        color: NoteColors.blackbold,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_regular",
                        fontSize: 16.sp,),),
                      Text(price, style: TextStyle(
                        color: NoteColors.blackbold,
                        fontWeight: FontWeight.w700,
                        fontFamily: "dm_medium",
                        fontSize: 16.sp,),),
                    ],
                  ),
                  SizedBox(height: 12.sp,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        height: 24, width: situationWidth,
                        decoration: BoxDecoration(
                          color:  situationColor,
                          borderRadius: BorderRadius.circular(8.sp),
                        ),child: Center(
                        child: Text(
                          situation, style: TextStyle(
                          color: NoteColors.white,
                          fontWeight: FontWeight.w400,
                          fontFamily: "pro_regular",
                          fontSize: 16.sp,),),
                      ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.sp,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        title, style: TextStyle(
                        color: NoteColors.gry,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_medium",
                        fontSize: 16.sp,),),
                      Text(
                        " DEEL APP", style: TextStyle(
                        color: NoteColors.blackbold,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_regular",
                        fontSize: 16.sp,),),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        category, style: TextStyle(
                        color: NoteColors.gry,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_medium",
                        fontSize: 16.sp,),),
                      Text(
                        " App Development", style: TextStyle(
                        color: NoteColors.blackbold,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_regular",
                        fontSize: 16.sp,),),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        start, style: TextStyle(
                        color: NoteColors.gry,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_medium",
                        fontSize: 16.sp,),),
                      Text(
                        DateFormat(' dd MMM yyyy').format(DateTime.now()), style: TextStyle(
                        color: NoteColors.blackbold,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_regular",
                        fontSize: 16.sp,),),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        end, style: TextStyle(
                        color: NoteColors.gry,
                        fontWeight: FontWeight.w600,
                        fontFamily: "pro_medium",
                        fontSize: 16.sp,),),
                      Text(
                        DateFormat("dd MM yyyy").format(DateTime.now()),
                        style: TextStyle(
                          color: NoteColors.blackbold,
                          fontWeight: FontWeight.w600,
                          fontFamily: "pro_regular",
                          fontSize: 16.sp,),),
                    ],
                  ),
                ],
              ).marginSymmetric(horizontal: 18.sp),
              Divider(color: NoteColors.gry,)
            ],
          ),
        ),

      ],
    );
  }
}
