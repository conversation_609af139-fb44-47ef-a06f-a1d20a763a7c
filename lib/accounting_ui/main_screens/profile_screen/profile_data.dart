import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/faq_screen.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/my_detail.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/my_order.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/profile_dialog.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/subscriptions_data.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:sizer/sizer.dart';

class ProfilesData extends StatelessWidget {
  const ProfilesData({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    return Scaffold(
      backgroundColor: NoteColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              // Professional Header with Profile
              _buildProfileHeader(),

              SizedBox(height: AppTheme.spacingLg),

              // Profile Menu Sections
              _buildAccountSection(),
              
              SizedBox(height: AppTheme.spacingLg),
              
              _buildPreferencesSection(),
              
              SizedBox(height: AppTheme.spacingLg),
              
              _buildSupportSection(),
              
              SizedBox(height: AppTheme.spacingLg),
              
              _buildLogoutSection(),

              SizedBox(height: AppTheme.spacingXl),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: NoteColors.background,
        boxShadow: [
          BoxShadow(
            color: NoteColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header Row
          Row(
            children: [
              Expanded(
                child: Text(
                  'Profile',
                  style: TextStyle(
                    color: NoteColors.textPrimary,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'inter_bold',
                  ),
                ),
              ),
              
              // Notification Button
              Container(
                decoration: BoxDecoration(
                  color: NoteColors.surface,
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  border: Border.all(
                    color: NoteColors.borderLight,
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () {},
                  icon: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      const Icon(
                        LucideIcons.bell,
                        size: AppTheme.iconSm,
                      ),
                      Positioned(
                        right: -2,
                        top: -2,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: NoteColors.error,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                  style: IconButton.styleFrom(
                    foregroundColor: NoteColors.textPrimary,
                    padding: EdgeInsets.all(AppTheme.spacingMd),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: AppTheme.spacingXl),

          // Profile Card
          Container(
            padding: EdgeInsets.all(AppTheme.spacingLg),
            decoration: BoxDecoration(
              color: NoteColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              border: Border.all(
                color: NoteColors.borderLight,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: NoteColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Profile Avatar
                Container(
                  width: 80.sp,
                  height: 80.sp,
                  decoration: BoxDecoration(
                    color: NoteColors.primaryContainer,
                    borderRadius: BorderRadius.circular(AppTheme.radiusXl),
                    border: Border.all(
                      color: NoteColors.primary.withValues(alpha: 0.2),
                      width: 3,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppTheme.radiusXl),
                    child: CachedNetworkImage(
                      imageUrl: "https://picsum.photos/200/200?random=profile",
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Icon(
                        LucideIcons.user,
                        color: NoteColors.primary,
                        size: AppTheme.iconXl,
                      ),
                      errorWidget: (context, url, error) => Icon(
                        LucideIcons.user,
                        color: NoteColors.primary,
                        size: AppTheme.iconXl,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: AppTheme.spacingLg),

                // Profile Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'John Doe',
                        style: TextStyle(
                          color: NoteColors.textPrimary,
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w700,
                          fontFamily: 'inter_bold',
                        ),
                      ),
                      
                      SizedBox(height: AppTheme.spacingXs),
                      
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          color: NoteColors.textSecondary,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'inter_regular',
                        ),
                      ),
                      
                      SizedBox(height: AppTheme.spacingMd),
                      
                      // Edit Profile Button
                      ElevatedButton.icon(
                        onPressed: () => Get.to(() => const MyDetail()),
                        icon: const Icon(
                          LucideIcons.edit,
                          size: AppTheme.iconXs,
                        ),
                        label: Text(
                          'Edit Profile',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'inter_semibold',
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: NoteColors.primaryContainer,
                          foregroundColor: NoteColors.primary,
                          elevation: 0,
                          padding: EdgeInsets.symmetric(
                            horizontal: AppTheme.spacingMd,
                            vertical: AppTheme.spacingSm,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection() {
    return _buildSection(
      title: 'Account',
      items: [
        _buildMenuItem(
          icon: LucideIcons.package,
          title: 'My Orders',
          subtitle: 'View your order history',
          onTap: () => Get.to(() => const MyOrder()),
        ),
        _buildMenuItem(
          icon: LucideIcons.user,
          title: 'Personal Details',
          subtitle: 'Manage your account information',
          onTap: () => Get.to(() => const MyDetail()),
        ),
        _buildMenuItem(
          icon: LucideIcons.creditCard,
          title: 'Subscriptions',
          subtitle: 'Manage your subscriptions',
          onTap: () => Get.to(() => const SubscriptionsData()),
        ),
      ],
    );
  }

  Widget _buildPreferencesSection() {
    return _buildSection(
      title: 'Preferences',
      items: [
        _buildMenuItem(
          icon: LucideIcons.bell,
          title: 'Notifications',
          subtitle: 'Manage notification settings',
          onTap: () {},
        ),
        _buildMenuItem(
          icon: LucideIcons.shield,
          title: 'Privacy & Security',
          subtitle: 'Control your privacy settings',
          onTap: () {},
        ),
        _buildMenuItem(
          icon: LucideIcons.globe,
          title: 'Language',
          subtitle: 'English',
          onTap: () {},
        ),
      ],
    );
  }

  Widget _buildSupportSection() {
    return _buildSection(
      title: 'Support',
      items: [
        _buildMenuItem(
          icon: LucideIcons.helpCircle,
          title: 'Help & FAQ',
          subtitle: 'Get help and find answers',
          onTap: () => Get.to(() => const FaqScreen()),
        ),
        _buildMenuItem(
          icon: LucideIcons.messageCircle,
          title: 'Contact Support',
          subtitle: 'Get in touch with our team',
          onTap: () {},
        ),
        _buildMenuItem(
          icon: LucideIcons.star,
          title: 'Rate App',
          subtitle: 'Share your feedback',
          onTap: () {},
        ),
      ],
    );
  }

  Widget _buildLogoutSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Container(
        decoration: BoxDecoration(
          color: NoteColors.surface,
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          border: Border.all(
            color: NoteColors.borderLight,
            width: 1,
          ),
        ),
        child: _buildMenuItem(
          icon: LucideIcons.logOut,
          title: 'Sign Out',
          subtitle: 'Sign out of your account',
          onTap: () => showProfileDialog(),
          textColor: NoteColors.error,
          iconColor: NoteColors.error,
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> items,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: NoteColors.textSecondary,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              fontFamily: 'inter_semibold',
            ),
          ),
          
          SizedBox(height: AppTheme.spacingMd),
          
          Container(
            decoration: BoxDecoration(
              color: NoteColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              border: Border.all(
                color: NoteColors.borderLight,
                width: 1,
              ),
            ),
            child: Column(
              children: items.map((item) {
                final index = items.indexOf(item);
                return Column(
                  children: [
                    item,
                    if (index < items.length - 1)
                      Divider(
                        color: NoteColors.borderLight,
                        height: 1,
                        indent: AppTheme.spacingXl + AppTheme.spacingMd,
                      ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? textColor,
    Color? iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacingLg),
        child: Row(
          children: [
            // Icon
            Container(
              padding: EdgeInsets.all(AppTheme.spacingSm),
              decoration: BoxDecoration(
                color: (iconColor ?? NoteColors.primary).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              ),
              child: Icon(
                icon,
                size: AppTheme.iconSm,
                color: iconColor ?? NoteColors.primary,
              ),
            ),

            SizedBox(width: AppTheme.spacingMd),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: textColor ?? NoteColors.textPrimary,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'inter_semibold',
                    ),
                  ),
                  
                  SizedBox(height: AppTheme.spacingXs),
                  
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: NoteColors.textSecondary,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'inter_regular',
                    ),
                  ),
                ],
              ),
            ),

            // Arrow
            Icon(
              LucideIcons.chevronRight,
              size: AppTheme.iconSm,
              color: NoteColors.textTertiary,
            ),
          ],
        ),
      ),
    );
  }
}
