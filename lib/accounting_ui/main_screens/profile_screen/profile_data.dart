import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/faq_screen.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/my_detail.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/my_order.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/profile_dialog.dart';
import 'package:accounting_app/accounting_ui/main_screens/profile_screen/profile_catagory/subscriptions_data.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

class ProfilesData extends StatelessWidget {
  const ProfilesData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            SizedBox(height: 14.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Profile", style: TextStyle(
                  color: NoteColors.blackbold,
                  fontWeight: FontWeight.w400,
                  fontFamily: "gen_semibold",
                  fontSize: 24.sp,),),
                SvgPicture.asset("assets/image/notify.svg",height: 42,width: 42,),
              ],
            ).marginSymmetric(horizontal: 18.sp),
            SizedBox(height: 20.sp,),
            _buildTile(SvgPicture.asset("assets/image/Box.svg",height: 24,width: 24,), "My Orders", (){context.openScreen(MyOrder());},Icon(Icons.arrow_forward_ios_rounded,color: NoteColors.b3b3b3,size: 20.sp),NoteColors.blackbold),
            SizedBox(height: 10.sp,),
             Container(
               height: 8,width: 380,
               decoration: BoxDecoration(
                 color: NoteColors.e5e5,
               ),
             ),
            _buildTile(SvgPicture.asset("assets/image/Details.svg",height: 24,width: 24,), "My Details", (){context.openScreen(MyDetail());},Icon(Icons.arrow_forward_ios_rounded,color: NoteColors.b3b3b3,size: 20.sp),Colors.black),
             Padding(
               padding: EdgeInsets.only(left: 30.sp,right: 25.sp),
               child: Divider(
                 color: NoteColors.e5e5,
                 thickness: 1,
               ),
             ),
            _buildTile(SvgPicture.asset("assets/image/card_membership.svg",height: 24,width: 24,), "Subscription", (){context.openScreen(SubscriptionsData());},Icon(Icons.arrow_forward_ios_rounded,color: NoteColors.b3b3b3,size: 20.sp),Colors.black),
            SizedBox(height: 13.sp),
            Container(
              height: 8,width: 380,
              decoration: BoxDecoration(
                color: NoteColors.e5e5,
              ),
            ),
            SizedBox(height: 13.sp,),
            _buildTile(SvgPicture.asset("assets/image/Question (1).svg",height: 24,width: 24,), "FAQs", (){context.openScreen(FaqScreen());},Icon(Icons.arrow_forward_ios_rounded,color: NoteColors.white,size: 20.sp),Colors.black),
            Padding(
              padding:  EdgeInsets.only(left: 30.sp,right: 25.sp),
              child: Divider(
                color: NoteColors.e5e5,
                thickness: 1,
              ),
            ),
            _buildTile(SvgPicture.asset("assets/image/Logout.svg",height: 24,width: 24,), "Logout", (){
              showDialog(context: context, builder: (BuildContext context) => ProfileDialog(),);
            },Icon(Icons.arrow_forward_ios_rounded,color: NoteColors.white,size: 20.sp),Colors.red),

          ],
        ),
      ),
    );
  }
  Widget _buildTile(Widget image,String title,void Function()? onTap,Widget? icon,Color color){
    return MyListTile(
      images: image,
      title: Text(title,style: TextStyle(color: color,fontFamily: "gen_regular",fontWeight: FontWeight.w500,fontSize: 16.sp),).marginOnly(right: 26.sp),
      trailing: Padding(
        padding:  EdgeInsets.all(16.sp),
        child: icon,
      ),
      onTap: onTap,
    );
  }
}
