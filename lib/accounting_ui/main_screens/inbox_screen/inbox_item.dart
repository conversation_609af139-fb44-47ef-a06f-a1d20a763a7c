import 'dart:math';

import 'package:accounting_app/accounting_ui/main_screens/inbox_screen/detail_chat.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/theme/app_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:random_x/random_x.dart';
import 'package:sizer/sizer.dart';

class InboxItem extends StatelessWidget {
  InboxItem({super.key});

  final RxList<Map<String, dynamic>> founds = List<Map<String, dynamic>>.from(
      nameList).obs;
  final RxString selectedFilter = 'All'.obs;

  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      founds.assignAll(List.from(nameList));
    } else {
      founds.assignAll(
        nameList.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    TextEditingController controller = TextEditingController();
    
    return Scaffold(
      backgroundColor: NoteColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Professional Header
            _buildHeader(),

            // Filter Tabs
            _buildFilterTabs(),

            // Search Section
            _buildSearchSection(controller),

            // Messages List
            Expanded(
              child: _buildMessagesList(),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: NoteColors.background,
        boxShadow: [
          BoxShadow(
            color: NoteColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Messages',
                  style: TextStyle(
                    color: NoteColors.textPrimary,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'inter_bold',
                  ),
                ),
                Text(
                  'Stay connected with your team',
                  style: TextStyle(
                    color: NoteColors.textSecondary,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'inter_regular',
                  ),
                ),
              ],
            ),
          ),
          
          // Notification Button
          Container(
            decoration: BoxDecoration(
              color: NoteColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              border: Border.all(
                color: NoteColors.borderLight,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () {},
              icon: Stack(
                clipBehavior: Clip.none,
                children: [
                  const Icon(
                    LucideIcons.bell,
                    size: AppTheme.iconSm,
                  ),
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: NoteColors.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
              style: IconButton.styleFrom(
                foregroundColor: NoteColors.textPrimary,
                padding: EdgeInsets.all(AppTheme.spacingMd),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    final filters = ['All', 'Unread', 'Groups', 'Archived'];
    
    return Container(
      height: 50,
      margin: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        separatorBuilder: (context, index) => SizedBox(width: AppTheme.spacingSm),
        itemBuilder: (context, index) {
          final filter = filters[index];
          return Obx(() => FilterChip(
                label: Text(filter),
                selected: selectedFilter.value == filter,
                onSelected: (selected) {
                  if (selected) {
                    selectedFilter.value = filter;
                  }
                },
                backgroundColor: NoteColors.surface,
                selectedColor: NoteColors.primaryContainer,
                checkmarkColor: NoteColors.primary,
                labelStyle: TextStyle(
                  color: selectedFilter.value == filter
                      ? NoteColors.primary
                      : NoteColors.textSecondary,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'inter_medium',
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                  side: BorderSide(
                    color: selectedFilter.value == filter
                        ? NoteColors.primary
                        : NoteColors.borderLight,
                    width: 1,
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMd,
                  vertical: AppTheme.spacingSm,
                ),
              ));
        },
      ),
    );
  }

  Widget _buildSearchSection(TextEditingController controller) {
    return Padding(
      padding: EdgeInsets.all(AppTheme.spacingLg),
      child: TextFormField(
        controller: controller,
        onChanged: _runFilter,
        decoration: InputDecoration(
          hintText: 'Search messages, contacts...',
          prefixIcon: const Icon(
            LucideIcons.search,
            size: AppTheme.iconSm,
          ),
          suffixIcon: IconButton(
            onPressed: () {},
            icon: const Icon(
              LucideIcons.sliders,
              size: AppTheme.iconSm,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    return Obx(() => ListView.separated(
          padding: EdgeInsets.symmetric(horizontal: AppTheme.spacingLg),
          itemCount: founds.length,
          separatorBuilder: (context, index) => 
              SizedBox(height: AppTheme.spacingSm),
          itemBuilder: (context, index) {
            final message = founds[index];
            return _buildMessageItem(message, index);
          },
        ));
  }

  Widget _buildMessageItem(Map<String, dynamic> message, int index) {
    final isUnread = Random().nextBool();
    final messageCount = Random().nextInt(5) + 1;
    final lastMessage = _generateRandomMessage();
    final time = _generateRandomTime();
    
    return InkWell(
      onTap: () => Get.to(() => DetailChat(
        name: message["name"] ?? "Unknown",
        image: message["image"] ?? "",
      )),
      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: isUnread ? NoteColors.primaryContainer.withValues(alpha: 0.3) : NoteColors.surface,
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          border: Border.all(
            color: isUnread ? NoteColors.primary.withValues(alpha: 0.2) : NoteColors.borderLight,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: NoteColors.shadowLight,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            // Profile Avatar with Online Status
            Stack(
              children: [
                Container(
                  width: 56.sp,
                  height: 56.sp,
                  decoration: BoxDecoration(
                    color: NoteColors.primaryContainer,
                    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                    child: CachedNetworkImage(
                      imageUrl: message["image"] ?? "",
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Icon(
                        LucideIcons.user,
                        color: NoteColors.primary,
                        size: AppTheme.iconMd,
                      ),
                      errorWidget: (context, url, error) => Icon(
                        LucideIcons.user,
                        color: NoteColors.primary,
                        size: AppTheme.iconMd,
                      ),
                    ),
                  ),
                ),
                
                // Online Status Indicator
                if (Random().nextBool())
                  Positioned(
                    right: 2,
                    bottom: 2,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: NoteColors.success,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: NoteColors.background,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            SizedBox(width: AppTheme.spacingMd),

            // Message Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          message["name"] ?? "Unknown",
                          style: TextStyle(
                            color: NoteColors.textPrimary,
                            fontSize: 16.sp,
                            fontWeight: isUnread ? FontWeight.w600 : FontWeight.w500,
                            fontFamily: isUnread ? 'inter_semibold' : 'inter_medium',
                          ),
                        ),
                      ),
                      Text(
                        time,
                        style: TextStyle(
                          color: isUnread ? NoteColors.primary : NoteColors.textTertiary,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'inter_regular',
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: AppTheme.spacingXs),
                  
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          lastMessage,
                          style: TextStyle(
                            color: isUnread ? NoteColors.textSecondary : NoteColors.textTertiary,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'inter_regular',
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      
                      if (isUnread)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppTheme.spacingSm,
                            vertical: AppTheme.spacingXs,
                          ),
                          decoration: BoxDecoration(
                            color: NoteColors.primary,
                            borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                          ),
                          child: Text(
                            messageCount.toString(),
                            style: TextStyle(
                              color: NoteColors.onPrimary,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'inter_semibold',
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {},
      backgroundColor: NoteColors.primary,
      foregroundColor: NoteColors.onPrimary,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      ),
      child: const Icon(
        LucideIcons.messagePlus,
        size: AppTheme.iconMd,
      ),
    );
  }

  String _generateRandomMessage() {
    final messages = [
      "Hey, how are you doing?",
      "Can we schedule a meeting?",
      "Thanks for the update!",
      "Let me know when you're free",
      "Great work on the project",
      "See you tomorrow",
      "Perfect, thanks!",
      "I'll send the documents",
      "Looking forward to it",
      "Have a great day!",
    ];
    return messages[Random().nextInt(messages.length)];
  }

  String _generateRandomTime() {
    final times = [
      "Just now",
      "2m ago",
      "5m ago",
      "1h ago",
      "2h ago",
      "Yesterday",
      "Monday",
      "Tuesday",
      "Last week",
    ];
    return times[Random().nextInt(times.length)];
  }
}
