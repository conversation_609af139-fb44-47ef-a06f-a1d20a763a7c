import 'dart:math';

import 'package:accounting_app/accounting_ui/main_screens/inbox_screen/detail_chat.dart';
import 'package:accounting_app/extensions/extension.dart';
import 'package:accounting_app/item_list/item_lists.dart';
import 'package:accounting_app/my_color/note_color.dart';
import 'package:accounting_app/my_widget/my_list_tile.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:random_x/random_x.dart';
import 'package:sizer/sizer.dart';

class InboxItem extends StatelessWidget {
  InboxItem({super.key});

  final RxList<Map<String, dynamic>> founds = List<Map<String, dynamic>>.from(
      nameList).obs;

  void _runFilter(String enterKeyboard) {
    if (enterKeyboard.isEmpty) {
      founds.assignAll(List.from(nameList));
    } else {
      founds.assignAll(
        nameList.where((call) =>
            call["name"]!.toLowerCase().contains(enterKeyboard.toLowerCase()))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    TextEditingController controller = TextEditingController();
    return Scaffold(
      backgroundColor: NoteColors.white,
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: 20.sp,),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Inbox", style: TextStyle(
                  color: NoteColors.blackbold,
                  fontWeight: FontWeight.w400,
                  fontFamily: "gen_semibold",
                  fontSize: 25.sp,),),
                SvgPicture.asset(
                  "assets/image/notify.svg", height: 42, width: 42,),
              ],
            ).marginSymmetric(horizontal: 18.sp),
            _buildTextField("Search...", controller, TextInputType.name,
                [ FilteringTextInputFormatter.singleLineFormatter,]),
            SizedBox(height: 10.sp,),
            Expanded(
              child: Obx(() {
                return ListView.builder(
                  itemCount: founds.length,
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (context, index) {
                    var personDetails = RndX.randomCompletePersonDetails();
                    var name = founds[index];
                    var education = personDetails.educationalBackground;
                    var num = Random().nextInt(10);
                    return getChat(context, name, education, num, index);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(String hint, TextEditingController controller,
      TextInputType keyboardType, List<TextInputFormatter>? inputFormatters) {
    return Padding(
      padding: EdgeInsets.only(left: 21.sp, right: 18.sp, top: 10.sp,),
      child: TextField(
        onChanged: _runFilter,
        autofocus: true,
        inputFormatters: inputFormatters,
        keyboardType: keyboardType,
        controller: controller,
        decoration: InputDecoration(
          filled: true,
          border: UnderlineInputBorder(
            borderSide: BorderSide(
              color: NoteColors.gry.withValues(alpha: .30),
              width: 1.5.sp,
            ),
          ),
          hintText: hint,
          hintStyle: TextStyle(
              color: NoteColors.blackbold.withValues(alpha: 0.50),
              fontFamily: "gen_regular",
              fontSize: 15.sp,
              fontWeight: FontWeight.w400),
          fillColor: NoteColors.white,
          contentPadding: EdgeInsets.symmetric(
              horizontal: 15.sp, vertical: 15.sp),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(
              color: NoteColors.gry,
              width: 1.5.sp,
            ),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(
              color: NoteColors.gry,
              width: 2.sp,
            ),
          ),
          prefixIcon: Padding(
            padding: EdgeInsets.all(16.sp),
            child: SvgPicture.asset("assets/image/searching.svg", height: 10,
              width: 10,
              colorFilter: ColorFilter.mode(NoteColors.gry, BlendMode.srcIn),),
          ),
        ),
      ),
    );
  }

  Widget getChat(BuildContext context, Map<String, dynamic> title,
      String subtitle, int read, int index) {
    var name = founds[index];
    var image = "https://picsum.photos/200/200?random=$index";
    String currentTime = DateFormat('hh:mm a').format(DateTime.now());
    return Padding(
      padding: EdgeInsets.only(top: 10.sp),
      child: MyListTile(
        onTap: () {
          context.openScreen(
              DetailChat(image: image, name: title, read: read,));
        },
        images: Stack(
          children: [
            Container(
              height: 60,
              width: 60,
              decoration: BoxDecoration(
                color: NoteColors.reds,
                image: DecorationImage(
                  image: CachedNetworkImageProvider(image),
                ),
                shape: BoxShape.circle,
              ),
            ),
            Positioned(
              top: 28.sp, left: 28.sp,
              child: Container(
                height: 10, width: 10,
                decoration: BoxDecoration(
                  color: read > 3 ? NoteColors.greenlight : NoteColors.time,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
        title: Text(
          name["name"]!,
          maxLines: 1,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            fontFamily: "poppins_bold",
          ),
        ),
        subtitle: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Support member",
              maxLines: 1,
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w400,
                fontFamily: "pro_regular",
                color: NoteColors.gry,
              ),
            ),
            Text(
              subtitle,
              maxLines: 1,
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
                fontFamily: "poppins_regular",
                color: read > 3 ? NoteColors.boldcount : NoteColors.time,
              ),
            ),
          ],
        ),
        trailing: Padding(
          padding: EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                currentTime,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: "poppins_regular",
                  color: NoteColors.time,
                ),
              ),
              Badge.count(
                isLabelVisible: read != 0,
                backgroundColor: NoteColors.redbach,
                textColor: NoteColors.white,
                count: read,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
