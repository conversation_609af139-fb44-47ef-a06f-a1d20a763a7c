import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../my_color/note_color.dart';

/// Professional EU-compliant theme system following Material Design 3 guidelines
class AppTheme {
  /// Professional spacing system
  static const double spacingXs = 4.0;

  static const double spacingSm = 8.0;

  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;

  /// Border radius system
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;

  static const double radiusMd = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 24.0;

  /// Icon sizes
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;

  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 48.0;

  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // Color Scheme - Professional and EU-compliant
      colorScheme: const ColorScheme.light(
        primary: NoteColors.primary,
        primaryContainer: NoteColors.primaryContainer,
        secondary: NoteColors.secondary,
        secondaryContainer: NoteColors.secondaryContainer,
        surface: NoteColors.surface,
        surfaceContainerHighest: NoteColors.surfaceVariant,
        error: NoteColors.error,
        errorContainer: NoteColors.errorLight,
        onPrimary: NoteColors.onPrimary,
        onSecondary: NoteColors.onSecondary,
        onSurface: NoteColors.onSurface,
        onError: NoteColors.onPrimary,
        outline: NoteColors.border,
        outlineVariant: NoteColors.borderLight,
        shadow: NoteColors.shadow,
      ),

      // App Bar Theme - Clean and Professional
      appBarTheme: const AppBarTheme(
        backgroundColor: NoteColors.background,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 1,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: NoteColors.textPrimary,
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'inter_semibold',
        ),
        iconTheme: IconThemeData(
          color: NoteColors.textPrimary,
          size: 20,
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
      ),

      // Navigation Bar Theme - Professional Bottom Navigation
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: NoteColors.background,
        surfaceTintColor: Colors.transparent,
        elevation: 8,
        height: 64,
        indicatorColor: NoteColors.primary,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return const TextStyle(
              fontSize: 12,
              fontFamily: 'inter_medium',
              fontWeight: FontWeight.w600,
              color: NoteColors.primary,
            );
          }
          return const TextStyle(
            fontSize: 11,
            fontFamily: 'inter_regular',
            fontWeight: FontWeight.w500,
            color: NoteColors.textSecondary,
          );
        }),
        iconTheme: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return const IconThemeData(
              color: NoteColors.onPrimary,
              size: 20,
            );
          }
          return const IconThemeData(
            color: NoteColors.textSecondary,
            size: 20,
          );
        }),
      ),

      // Elevated Button Theme - Professional Primary Buttons
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: NoteColors.primary,
          foregroundColor: NoteColors.onPrimary,
          elevation: 2,
          shadowColor: NoteColors.shadow,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'inter_semibold',
          ),
        ),
      ),

      // Input Decoration Theme - Professional Form Fields
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: NoteColors.background,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: NoteColors.border,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: NoteColors.border,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: NoteColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: NoteColors.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: NoteColors.error,
            width: 2,
          ),
        ),
      ),

      // Icon Theme - Consistent Icon Styling
      iconTheme: const IconThemeData(
        color: NoteColors.textSecondary,
        size: 20,
      ),
    );
  }

  // Private constructor to prevent instantiation
  AppTheme._();
}
