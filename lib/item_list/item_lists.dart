
import 'package:intl/intl.dart';

final List<Map<String, String>> countryList = [
  {
    "name" : "Taiwan",
  },
  {
    "name" : "United States",
  },
  {
    "name" : "United Kingdom",
  },
  {
    "name" : "Ukraine",
  },
  {
    "name" : "Uganda",
  },
  {
    "name" : "Uruguay",
  },
  {
    "name" : "Uzbekistan",
  },
  {
    "name" : "Vietnam",
  },
  {
    "name" : "Uganda",
  },
  {
    "name" : "Uruguay",
  },
  {
    "name" : "Uzbekistan",
  },
  {
    "name" : "Vietnam",
  },

];
final List<Map<String, String>> gender = [
  {
    "image" : "assets/image/Vector (8).svg",
    "name" : "Male",
  },
  {
    "image" : "assets/image/Frame 26 (2).svg",
    "name" : "Female",
  },
];
final List<Map<String, String>> subscription = [
  {
    "title" : "One time",
    "sub" : "Lorem Ipsum is simply..",
    "price" : "\$99.99"
  },
  {
    "title" : "Monthly",
    "sub" : "Lorem Ipsum is simply..",
    "price" : "\$39.99/year"
  },

];
final List<Map<String, String>> homeData = [
  {
    "name" : "<PERSON>",
    "exp" :  "6 Years experience ",
    "client" : " 78 Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Liam",
    "exp" :  "3 Years experience ",
    "client" : " 200+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Oliver",
    "exp" :  "2 Years experience ",
    "client" : " 100+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Ethan",
    "exp" :  "4 Years experience ",
    "client" : " 500+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Mariam ",
    "exp" :  "9 Years experience ",
    "client" : " 950+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Kathrine",
    "exp" :  "5 Years experience ",
    "client" : " 400+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Olivery",
    "exp" :  "6 Years experience ",
    "client" : " 1000+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    "name" : "Elsa gen",
    "exp" :  "8 Years experience ",
    "client" : " 1200+ Clients",
    "image": "https://randomuser.me/api/portraits/men/1.jpg",
  },
];
final List<String> randomImages = [
  "https://randomuser.me/api/portraits/men/1.jpg",
  "https://randomuser.me/api/portraits/men/2.jpg",
  "https://randomuser.me/api/portraits/women/1.jpg",
  "https://randomuser.me/api/portraits/women/2.jpg",
  "https://randomuser.me/api/portraits/men/3.jpg",
  "https://randomuser.me/api/portraits/women/3.jpg",
  "https://randomuser.me/api/portraits/women/4.jpg",
  "https://randomuser.me/api/portraits/women/7.jpg",
  "https://randomuser.me/api/portraits/women/6.jpg",
  "https://randomuser.me/api/portraits/women/9.jpg",
];
final List<Map<String, String>> nameList = [
  {
    "name" : "Ali",
  },
  {
    "name" : "Ahmad",
  },
  {
    "name" : "Moon",
  },
  {
    "name" : "Hassan",
  },
  {
    "name" : "Hussain",
  },
  {
    "name" : "Shan",
  },
  {
    "name" : "Akram",
  },
];
final List<Map<String, String>> callHistory = [
  {
    "name" : "Abdul Qadir",
    "subName" : "Outgoing call, 1 m 12 secs",
     "time" : DateFormat('hh:mm a').format(DateTime.now()),
  },
  {
    "name" : "Oliver",
    "subName" : "Outgoing call, 6 m 34 secs",
    "time" : DateFormat('hh:mm a').format(DateTime.now()),
  },
  {
    "name" : "Kathrine",
    "subName" : "Outgoing call, 3 m 26 secs",
    "time" : DateFormat('hh:mm a').format(DateTime.now()),
  },
  {
    "name" : "Muhammad Ali",
    "subName" : "Outgoing call, 3 m 26 secs",
    "time" : DateFormat('hh:mm a').format(DateTime.now()),
  },
];
List<String>  faqList = [
  "General",
  "Account",
  "Service",
  "Payment",
];
final List<Map<String, String>> faqQuestion = [
  {
    "faqsQuestion": "How do I make a purchase?",
    "faqAnswer": "When you find a product you want to purchase, tap on it to view the product details. Check the price, description, and available options (if applicable), and then tap the Add to Cart button. Follow the on-screen instructions to complete the purchase, including providing shipping details and payment information.",
  },
  {
    "faqsQuestion": "What payment methods are accepted?",
    "faqAnswer": "The top 8 payment methods are credit cards, debit cards, Automated Clearing House (ACH) transfers, cash, paper checks, eChecks, digital payments, and mon.",
  },
  {
    "faqsQuestion": "How do I track my orders?",
    "faqAnswer": "Whether you are the sender or recipient, you can track your item: Online: Use USPS Tracking® on the United States Postal Service® website. By text: Send a text to 28777 (2USPS) with your tracking number as the content of the message. Standard message and data rates may apply.",
  },
  {
    "faqsQuestion": "Can I cancel or return an order?",
    "faqAnswer": "If you want to change or cancel your order, contact the merchant directly. If they have already processed a charge for the order, merchants can initiate refunds.",
  },
  {
    "faqsQuestion": "How can I contact customer support for assistance?",
    "faqAnswer": "Effective communication (including effective listening),as mentioned earlier, is crucial in helping your customer service team solve customers' issues to their satisfaction.",
  },
];