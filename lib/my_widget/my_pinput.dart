import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:sizer/sizer.dart';
class MyPinput extends StatelessWidget {
  const MyPinput({super.key});
  @override
  Widget build(BuildContext context) {
    final PinInputControllers controllers = Get.put(PinInputControllers());
    final defaultPinTheme = PinTheme(
      padding: EdgeInsets.all(8),
      height: 47,
      width: 47,
      textStyle: TextStyle(
        fontSize: 18.sp,
        color: Colors.black,
        fontFamily: "gen_bold",
        fontWeight: FontWeight.w700,

      ),
      decoration: BoxDecoration(
        color: NoteColors.pinput,
        borderRadius: BorderRadius.circular(7.sp),
        border: Border.all(color: NoteColors.pinput),
      ),
    );
    return Column(
      children: [
        Center(
          child: Form(
            child: Padding(
              padding: EdgeInsets.only(left: 16.sp,right: 16.sp,top: 20.sp),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Pinput(
                      scrollPadding: EdgeInsets.all(5),
                      showCursor: true,
                      focusNode: controllers.focusNode,
                      defaultPinTheme: defaultPinTheme,
                      controller: controllers.pinController,
                      length: 6,
                      onChanged: (pin) {
                        print("Enter pin: $pin");
                      },
                      onCompleted: (pin) {
                        print("Enter pin: $pin");
                      },
                      animationDuration: Duration(milliseconds: 200),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
class PinInputControllers extends GetxController {
  final TextEditingController pinController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(Get.context!).requestFocus(focusNode);
    });
  }
  @override
  void onClose() {
    pinController.dispose();
    focusNode.dispose();
    super.onClose();
  }
}
