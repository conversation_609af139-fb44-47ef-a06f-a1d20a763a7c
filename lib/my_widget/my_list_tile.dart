import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class MyListTile extends StatelessWidget {
  MyListTile({super.key, this.images,this.shape, this.title,this.subtitle,this.onTap ,this.tileColor, this.trailing});
  final  Widget? images;
  final  Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  ShapeBorder? shape;
  void Function()? onTap;
  Color? tileColor;
  @override
  Widget build(BuildContext context) {
    return  Padding(
      padding:  EdgeInsets.only(left: 12.sp),
      child: ListTile(
        shape: shape,
        leading: images,
        title: title,
        subtitle: subtitle,
        onTap: onTap,
        tileColor:tileColor,
        trailing: trailing,
      ),
    );
  }
}