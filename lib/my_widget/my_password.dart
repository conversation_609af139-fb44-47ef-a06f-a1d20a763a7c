import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class MyPassword extends StatefulWidget {
  final TextEditingController controller;
  final Function(bool) inValid;
  final String? errortext;
  final String? hintText;
  final String? seconderror;
  const MyPassword({super.key, required this.controller, required this.inValid,this.hintText,this.errortext,this.seconderror});
  @override
  _MyPasswordState createState() => _MyPasswordState();
}
class _MyPasswordState extends State<MyPassword> {
  bool _isValid = false;
  String? _errorText;
  String? error;
  var password = true;
  void pass(){
    setState(() {
      password = !password;
    });

  }
  void _validate(String value) {
    setState(() {
      if (value.length < 6)  {
        _isValid = false;
        _errorText = widget.errortext;
      } else if ((
          !value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')) ||
              !value.contains(RegExp(r'[0-9]')) ||
              !value.contains(RegExp(r'[a-z]')) ||
              !value.contains(RegExp(r'[A-Z]')))){
        _errorText = widget.errortext;
      } else {
        _isValid = true;
        _errorText = null;
      }
    });
    widget.inValid(_isValid);
  }
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          obscureText: password,
          controller: widget.controller,
          onChanged: _validate,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(horizontal: 18.sp,vertical: 18.sp),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.sp),
                borderSide: BorderSide(
                  color: _isValid ? NoteColors.bluePrimery : NoteColors.reds,
                ),
              ),
              hintText: widget.hintText,
              hintStyle: TextStyle(
                  color: NoteColors.gry,
                  fontFamily: "pro_regular",fontSize: 16.sp,fontWeight: FontWeight.w400),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14.sp),
                borderSide: BorderSide(
                  color: _isValid ? NoteColors.bluePrimery : NoteColors.d9d9,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14.sp),
                borderSide: BorderSide(
                  color: _isValid ? NoteColors.bluePrimery : NoteColors.reds,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14.sp),
                borderSide: BorderSide(
                  color: NoteColors.reds,
                ),
              ),
              suffixIcon:   IconButton(
                  onPressed: () {
                    pass();
                  }, icon: Icon(
                password ? Icons.visibility_off_outlined : Icons.visibility,color: NoteColors.gry,) )
          ),
        ),
        if(_errorText != null)
          Padding(
            padding:  EdgeInsets.only(left: 8.sp),
            child: Text('$_errorText',
              style: TextStyle(
                color: NoteColors.reds,
                fontFamily: "poppins_regular",
                fontWeight: FontWeight.w500,
                fontSize: 15.sp,height: 2.0,
              ),),
          )
      ],
    );
  }
}

//validator: (value) {
//                           if (value == null || value.isEmpty) {
//                             return "Password cannot be empty";
//                           } else if (value.contains('@'))
//                               ) {
//                             return "Invalid Email";
//                           } else {
//                             return null;
//                           }
//                         },