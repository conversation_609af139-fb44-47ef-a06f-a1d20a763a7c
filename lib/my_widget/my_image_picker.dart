import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
class ImagePickerController extends GetxController {
  RxString imagePath = ''.obs;
  Future<void> getImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != Null) {
      imagePath.value = image!.path.toString();
    } else {
      if (kDebugMode) {
        print("No image select");
      }
    }
  }

  ///Camera access
  RxString imagePaths = ''.obs;
  Future<void> getCamera() async {
    final ImagePicker picker = ImagePicker();
    final XFile? camera = await picker.pickImage(source: ImageSource.camera);
    if (camera != Null) {
      imagePaths.value = camera!.path.toString();
    } else {
      if (kDebugMode) {
        print("No image select");
      }
    }
  }
}


