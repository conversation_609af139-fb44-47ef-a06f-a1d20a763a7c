// import 'package:accounting_app/my_color/note_color.dart';
// import 'package:flutter/material.dart';
// import 'package:sizer/sizer.dart';
// class MyEmailTextfield extends StatefulWidget {
//   final TextEditingController controller;
//   final Function(bool) inValid;
//   String? errortext;
//   String? seconderror;
//   String? hintText;
//   String? Function(String?)? validator;
//   MyEmailTextfield({super.key, required this.controller, required this.inValid,this.hintText,this.errortext,this.validator,this.seconderror});
//   @override
//   _MyEmailTextfieldState createState() => _MyEmailTextfieldState();
// }
// class _MyEmailTextfieldState extends State<MyEmailTextfield> {
//   bool _isValid = false;
//   String? _errorText;
//   String? error;
//   void _validate(String value) {
//
//     setState(() {
//       var regex =  RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
//       if (value == null || value.isEmpty){
//         _isValid = false;
//         _errorText = widget.errortext;
//       } else if(!regex.hasMatch(value)){
//         _errorText = widget.seconderror;
//       } else {
//         _isValid = true;
//         _errorText = null;
//       }
//     });
//     widget.inValid(_isValid);
//   }
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         TextFormField(
//           controller: widget.controller,
//           validator: widget.validator,
//           onChanged: _validate,
//           autovalidateMode: AutovalidateMode.onUserInteraction,
//           decoration: InputDecoration(
//             border: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(14.sp),
//               borderSide: BorderSide(
//                 color: _isValid ? Colors.green : NoteColors.backgrond,
//               ),
//             ),
//             contentPadding: EdgeInsets.symmetric(
//                 vertical: 17.sp,horizontal: 15.sp),
//             hintText: widget.hintText,
//             hintStyle: TextStyle(
//                 color: NoteColors.gryText,fontFamily: "gen_regular",fontSize: 16.5.sp,fontWeight: FontWeight.w400),
//             enabledBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(8.0),
//               borderSide: BorderSide(
//                 color: _isValid ? Colors.green : NoteColors.backgrond,
//               ),
//             ),
//             focusedBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(8.0),
//               borderSide: BorderSide(
//                 color: _errorText != null ? Colors.red : Colors.green,
//               ),
//             ),
//             errorBorder: OutlineInputBorder(
//               borderRadius: BorderRadius.circular(8.0),
//               borderSide: BorderSide(
//                 color: Colors.red,
//               ),
//             ),
//             suffixIcon: _errorText != null ?  Icon(
//               Icons.error_outline,color: NoteColors.reds,) : Icon(
//                 _isValid ? Icons.check_circle_outline_outlined :null, color: Colors.green),
//           ),
//         ),
//         if(_errorText != null)
//           Padding(
//             padding:  EdgeInsets.only(left: 8.sp),
//             child: Text('$_errorText',
//               style: TextStyle(
//                 color: NoteColors.reds,
//                 fontFamily: "gen_regular",
//                 fontWeight: FontWeight.w500,
//                 fontSize: 15.sp,height: 2.0,
//               ),),
//           )
//       ],
//     );
//   }
// }

import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
class MyEmail extends StatefulWidget {
  final Function(bool)? inValid;
  final String? errorText;
  final String? hintText;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;
  final Color? fillColor;
  final TextEditingController? controller;
  const MyEmail(
      {
        super.key,
        this.contentPadding,
        this.inValid,
        this.hintText,
        this.errorText,
        this.validator,
        this.inputFormatters,
        this.keyboardType,
        this.fillColor,
        this.controller,
      });
  @override
  _MyEmailState createState() => _MyEmailState();
}
class _MyEmailState extends State<MyEmail> {
  bool _isValid = false;
  String? _errorText;
  String? error;
  void _validate(String value) {
    setState(() {
      var regex =  RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (value.isEmpty) {
        _isValid = false;
        _errorText = widget.errorText;
      } else if((!regex.hasMatch(value))){
          _errorText = widget.errorText;
      } else {
        _isValid = true;
        _errorText = null;
      }
    });
    widget.inValid!(_isValid);
  }
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(

          controller: widget.controller,
          keyboardType: widget.keyboardType,
          validator: widget.validator,
          onChanged: _validate,
          inputFormatters: widget.inputFormatters,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            filled: true,
            fillColor: widget.fillColor,
            contentPadding: widget.contentPadding,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14.sp),
              borderSide: BorderSide(
                color: _isValid ? NoteColors.bluePrimery : NoteColors.blackbold.withValues(alpha: 0.20),
              ),
            ),
            hintText: widget.hintText,
            hintStyle: TextStyle(
                color: NoteColors.gry,
                fontFamily: "pro_regular",
                fontSize: 16.sp,
                fontWeight: FontWeight.w400),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14.sp),
              borderSide: BorderSide(
                color: _isValid ? NoteColors.bluePrimery : NoteColors.d9d9,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14.sp),
              borderSide: BorderSide(
                color: _isValid ? NoteColors.bluePrimery : NoteColors.reds,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: Colors.red,
              ),
            ),
            suffixIcon: _isValid
                ? Icon(
                Icons.check_circle_rounded, color: NoteColors.bluePrimery)
                : _errorText != null
                ? Icon(
              Icons.error_outline,
              color: NoteColors.reds,
            )
                : null,
          ),
        ),
        if(_errorText != null)
          Padding(
            padding:  EdgeInsets.only(left: 8.sp),
            child: Text('$_errorText',
              style: TextStyle(
                color: NoteColors.reds,
                fontFamily: "poppins_regular",
                fontWeight: FontWeight.w500,
                fontSize: 15.sp,height: 2.0,
              ),),
          )
      ],
    );
  }
}