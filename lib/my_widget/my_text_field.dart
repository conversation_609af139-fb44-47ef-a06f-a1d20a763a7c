import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
class MyTextField extends StatefulWidget {
  final Function(bool)? inValid;
  String? errorText;
  String? hintText;
  String? Function(String?)? validator;
  TextInputType? keyboardType;
  EdgeInsetsGeometry? contentPadding;
  List<TextInputFormatter>? inputFormatters;
  Color? fillColor;
  TextEditingController? controller;
  Widget? suffixIcon;
  MyTextField(
      {
        super.key,
        this.suffixIcon,
        this.contentPadding,
        this.inValid,
        this.hintText,
        this.errorText,
        this.validator,
        this.inputFormatters,
        this.keyboardType,
        this.fillColor,
        this.controller,
      });
  @override
  _MyTextFieldState createState() => _MyTextFieldState();
}
class _MyTextFieldState extends State<MyTextField> {
  bool _isValid = false;
  String? _errorText;
  String? error;
  void _validate(String value) {
    setState(() {
      if (value.length < 8) {
        _isValid = false;
        _errorText = widget.errorText;
      }
      else {
        _isValid = true;
        _errorText = null;
      }
    });
    widget.inValid!(_isValid);
  }
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: widget.controller,
          keyboardType: widget.keyboardType,
          validator: widget.validator,
          onChanged: _validate,
          inputFormatters: widget.inputFormatters,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            filled: true,
            fillColor: widget.fillColor,
            contentPadding: widget.contentPadding,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14.sp),
              borderSide: BorderSide(
                color: _isValid ? NoteColors.bluePrimery : NoteColors.blackbold.withValues(alpha: 0.20),
              ),
            ),
            hintText: widget.hintText,
            hintStyle: TextStyle(
                color: NoteColors.gry,
                fontFamily: "pro_regular",
                fontSize: 16.sp,
                fontWeight: FontWeight.w400),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14.sp),
              borderSide: BorderSide(
                color: _isValid ? NoteColors.bluePrimery : NoteColors.d9d9,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(14.sp),
              borderSide: BorderSide(
                color: _isValid ? NoteColors.bluePrimery : NoteColors.reds,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide(
                color: Colors.red,
              ),
            ),
            suffixIcon:  _isValid
                ? Icon(
                Icons.check_circle_rounded, color: NoteColors.bluePrimery)
                :_errorText != null ?  Icon(
              Icons.error_outline,
              color: NoteColors.reds,
            ) : null,
          ),
        ),
        if(_errorText != null)
          Padding(
            padding:  EdgeInsets.only(left: 8.sp),
            child: Text('$_errorText',
              style: TextStyle(
                color: NoteColors.reds,
                fontFamily: "poppins_regular",
                fontWeight: FontWeight.w500,
                fontSize: 15.sp,height: 2.0,
              ),),
          )
      ],
    );
  }
}