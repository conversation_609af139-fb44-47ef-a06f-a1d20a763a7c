import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
class  MyUtils{
  static PreferredSizeWidget appbar({
    required String title,String? fontFamily,double? fontSize, Color? color, backgroundColor,surfaceTintColor,bool backOnLeft = true
  }) {
    return AppBar(
      surfaceTintColor: surfaceTintColor,
      backgroundColor: backgroundColor,
      automaticallyImplyLeading: backOnLeft,
      centerTitle: true,
      title: Text(title,style: TextStyle(
          color: color,
          fontFamily: fontFamily,fontSize: fontSize,fontWeight: FontWeight.w500),),
      leading: backOnLeft ?  IconButton(
          onPressed: (){
            Get.back();
          }, icon: Padding(
        padding:  EdgeInsets.all(8.0),
        child: Icon(Icons.arrow_back_ios_rounded),
      )) : null,
      actions: [
        if(!backOnLeft)
          IconButton(
              onPressed: (){Get.back();},
              icon: Icon(Icons.close,color: NoteColors.blackbold,))
      ],

    );
  }
}