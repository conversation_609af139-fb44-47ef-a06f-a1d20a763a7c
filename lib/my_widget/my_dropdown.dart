import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
class MyDropDown extends StatelessWidget {
  final String selectedValue;
  final List<String> items;
  final ValueChanged<String?> onChanged;
  List<BoxShadow>? boxShadow;
  BorderRadiusGeometry? borderRadius;
   MyDropDown({
    super.key,
    required this.selectedValue,
    required this.items,
    required this.onChanged,
    this.boxShadow,
     this.borderRadius,
  });
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
          padding:  EdgeInsets.only(left: 18.sp,right: 18.sp,top: 8.sp,),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10.sp),
            height: 52,
            width: 324,
            decoration: BoxDecoration(
              color: NoteColors.white,
              borderRadius: borderRadius,
              border: Border.all(color: NoteColors.e6e6e6),
              boxShadow: boxShadow,
            ),
            child: DropdownButton<String>(
              borderRadius: BorderRadius.circular(15.sp),
              elevation: 0,
              dropdownColor: NoteColors.white,
              value: selectedValue,
              onChanged: onChanged,
              items: items.map((String item) {
                return DropdownMenuItem(
                  value: item,
                  child: Padding(
                    padding: EdgeInsets.only(left: 12.sp),
                    child: Text(
                      item,
                      style: TextStyle(
                        color: NoteColors.blackbold,
                        fontFamily: "pro_medium",
                        fontWeight: FontWeight.w400,
                        fontSize: 15.sp,
                      ),
                    ),
                  ),
                );
              }).toList(),
              underline: SizedBox(),
              isExpanded: true,
              style: TextStyle(
                color: NoteColors.blackbold,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                fontFamily: "poppins_regular",
              ),
              icon: Icon(
                Icons.keyboard_arrow_down_sharp,
                size: 22.sp,
                color: NoteColors.blackbold,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
