import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
class MySwitchButton extends StatelessWidget {
  MySwitchButton({super.key,required this.value,this.onChange,this.activeColor,});
  bool value;
  void Function(bool)? onChange;
  Color? activeColor;
  @override
  Widget build(BuildContext context) {
    return Transform.scale(
        scale: 0.70,
        child: Switch(
          materialTapTargetSize:  MaterialTapTargetSize.shrinkWrap,
          autofocus: true,
          activeColor: Colors.white,
          inactiveThumbColor: NoteColors.white,
          activeTrackColor: NoteColors.bluePrimery,
          inactiveTrackColor: NoteColors.backgrond,
          hoverColor: NoteColors.yellow,
          trackOutlineColor: WidgetStatePropertyAll(Colors.transparent),
          value: value,
          onChanged: onChange,
        )
    );
  }
}