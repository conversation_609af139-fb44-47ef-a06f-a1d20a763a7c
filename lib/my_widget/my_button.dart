import 'package:flutter/material.dart';
import 'package:get/get.dart';
class MyButton extends StatelessWidget {
  MyButton({super.key,required this.onPressed,this.fontSize,this.fontFamily,this.shape,this.text,this.buttonColor,this.paddingVertical,this.textColor});
  void Function()? onPressed;
  Color? buttonColor;
  String? text,fontFamily;
  EdgeInsetsGeometry? paddingVertical;
  OutlinedBorder? shape;
  Color? textColor;
  double? fontSize;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          elevation: 0,
          shape: shape,
          padding: paddingVertical,
        ),
        onPressed: onPressed,
        child: Text(text!,
          style: TextStyle(
          color: textColor,
          fontFamily: fontFamily,
          fontSize: fontSize,
          fontWeight: FontWeight.w500,
        ),),
      ),
    );
  }
}