import 'package:accounting_app/accounting_ui/authentication/splash_screen.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import 'theme/app_theme.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  _cameras = await availableCameras();
  runApp(const MyApp());
}

late List<CameraDescription> _cameras;

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return Sizer(builder: (_, __, ___) {
      return GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'Flutter Demo',
        defaultTransition: Transition.fadeIn,
        transitionDuration: Duration(milliseconds: 800),
        theme: AppTheme.lightTheme,
        home: SplashScreen(),
      );
    });
  }
}
