import 'package:accounting_app/accounting_ui/authentication/splash_screen.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sizer/sizer.dart';

import 'my_color/note_color.dart';
late List<CameraDescription> _cameras;

  Future<void> main() async {
   WidgetsFlutterBinding.ensureInitialized();
  _cameras = await availableCameras();
  runApp(const MyApp());
}
class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return Sizer(builder: (_,__,___) {
      return GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'Flutter Demo',
        defaultTransition: Transition.fadeIn,
        transitionDuration: Duration(milliseconds: 800),
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          useMaterial3: true,
          navigationBarTheme: NavigationBarThemeData(
            labelTextStyle: WidgetStateProperty.resolveWith((states) {
              if(states.contains(WidgetState.selected)) {
                return TextStyle(
                  fontSize: 13.0,
                  fontFamily: "roboto_regular",
                  fontWeight: FontWeight.w600,
                  color: NoteColors.bluePrimery,
                );
              }
              return TextStyle(
                fontSize: 12.0,
                fontFamily: "roboto_regular",
                fontWeight: FontWeight.w500,
                color: NoteColors.gry,
              );
            }),
          ),
        ),
        home: SplashScreen(),
      );
    });

  }
}


