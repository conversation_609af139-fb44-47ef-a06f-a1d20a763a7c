import 'dart:async';

import 'package:accounting_app/my_color/note_color.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
class MyController extends GetxController {

 ///my detail dropdown
  RxString gender = 'Male'.obs;
  RxList<String> genderList = ['Male','Female','Custom'].obs;
  void selectGender(String value) {
    gender.value = value;
  }
  var currentTime = ''.obs;
  @override
  void onInit() {
    super.onInit();
    updateTime();
    Timer.periodic(Duration(seconds: 1), (timer) {
      currentTime.value = DateFormat('mm:ss ').format(DateTime.now());
    });
  }
  void updateTime() {
    currentTime.value = DateFormat('mm:ss ').format(DateTime.now());
  }
  RxList<bool> buttonChange = <bool>[].obs;
  void initializeButton(int count) {
    buttonChange.value = List.generate(count, (_) => true);
  }
  void toggleButton(int index) {
    buttonChange[index] = !buttonChange[index];
  }

  var current = 0.obs;
  RxBool switched = false.obs;
  void toggleSwitch() {
    switched.value = !switched.value;
  }
  ///mydropdown
  RxString country = 'USA'.obs;
  RxList<String> countryList = ['USA', 'Canada', 'India', 'Australia'].obs;
  void selectCountry(String value) {
    country.value = value;
  }
}
class ImagepickerController extends GetxController {
  RxString imagePath = ''.obs;
  Future<void> getImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != Null) {
      imagePath.value = image!.path.toString();
    } else {
      null;
    }
  }
  ///Camera access
  RxString imagePaths = ''.obs;
  Future<void> getCamera() async {
    final ImagePicker picker = ImagePicker();
    final XFile? camera = await picker.pickImage(source: ImageSource.camera);
    if (camera != Null) {
      imagePaths.value = camera!.path.toString();
    } else {
      null;
    }
  }
}
class DatePickerController extends GetxController {
  var dateController = TextEditingController().obs;
  var selectedDate = DateTime.now().obs;
  @override
  void onInit() {
    super.onInit();
    dateController.value.text =
        DateFormat('EEE, d MMM yyyy').format(selectedDate.value);
  }
  Future<void> selectDate(BuildContext context) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(3000),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: NoteColors.bluePrimery,
              onSurface: NoteColors.blackbold,
              onPrimary: NoteColors.white,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                backgroundColor: NoteColors.white,
                surfaceTintColor: NoteColors.blackbold,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    selectedDate.value = picked ?? selectedDate.value;
    dateController.value.text =
        DateFormat('EEE, d MMM yyyy').format(selectedDate.value);
    }
}
class EmojiController extends GetxController{
  var isEmoji = false.obs;
  FocusNode focusNode = FocusNode();
  TextEditingController textController = TextEditingController();
  @override
  void onInit(){
    super.onInit();
    focusNode.addListener((){
      if (focusNode.hasFocus) {
        isEmoji.value = false;
      }
    });
  }
  @override
  void onClose(){
     textController.dispose();
  }
}


